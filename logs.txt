=== LOOP RESTART - MIDI EDITOR STATE ===
BandLabMidiEditor.tsx:238 Current MIDI Notes:
BandLabMidiEditor.tsx:240   Note 1: Pitch=72, Start=0.500, End=1.000, Duration=0.500, ID=rec_session_1750002365836_pl37hejoq_72_ri93patby
BandLabMidiEditor.tsx:240   Note 2: Pitch=71, Start=1.000, End=1.500, Duration=0.500, ID=rec_session_1750002365836_pl37hejoq_71_jmnbs0ahu
BandLabMidiEditor.tsx:240   Note 3: Pitch=72, Start=4.500, End=5.000, Duration=0.500, ID=rec_session_1750002365836_pl37hejoq_72_c5wvy7uss
BandLabMidiEditor.tsx:240   Note 4: Pitch=71, Start=5.000, End=5.500, Duration=0.500, ID=rec_session_1750002365836_pl37hejoq_71_2oebh8mvb
BandLabMidiEditor.tsx:240   Note 5: Pitch=72, Start=5.500, End=6.750, Duration=1.250, ID=rec_session_1750002365836_pl37hejoq_72_jcddvxvpe
BandLabMidiEditor.tsx:242 Total Notes: 5
BandLabMidiEditor.tsx:243 === END LOOP RESTART STATE ===
BandLabMidiEditor.tsx:196 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:196
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
flushPendingEffects @ react-dom_client.js?v=fbe82bdf:11471
flushSpawnedWork @ react-dom_client.js?v=fbe82bdf:11445
commitRoot @ react-dom_client.js?v=fbe82bdf:11276
commitRootWhenReady @ react-dom_client.js?v=fbe82bdf:10695
performWorkOnRoot @ react-dom_client.js?v=fbe82bdf:10640
performSyncWorkOnRoot @ react-dom_client.js?v=fbe82bdf:11838
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=fbe82bdf:11735
processRootScheduleInMicrotask @ react-dom_client.js?v=fbe82bdf:11757
(anonymous) @ react-dom_client.js?v=fbe82bdf:11852Understand this error
7BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
VM21078:414 
            
            
           POST https://www.youtube.com/api/stats/qoe?fmt=248&afmt=251&cpn=4EhP8F7dMjMuSm7p&el=embedded&ns=yt&fexp=v1%2C24004644%2C562043%2C26443548%2C53408%2C34656%2C106030%2C18644%2C14869%2C75925%2C26895%2C9252%2C3479%2C690%2C12340%2C23206%2C7703%2C7476%2C2%2C4525%2C20976%2C9563%2C16755%2C2795%2C3415%2C1091%2C8887%2C6114%2C2660%2C2040%2C590%2C1797%2C4313%2C1920%2C679%2C80%2C1550%2C2698%2C2301%2C5877%2C3303%2C1090%2C6781&cl=769383743&seq=10&docid=qTl8TLcARco&ei=eOpOaNf9D4zlxN8PzI_egAE&event=streamingstats&plid=AAY3nik6PcXZ1EDy&cbrand=apple&cbr=Chrome&cbrver=*********&c=WEB_EMBEDDED_PLAYER&cver=1.20250609.22.00&cplayer=UNIPLAYER&cos=Macintosh&cosver=10_15_7&cplatform=DESKTOP&cmt=96.651:6.355,96.904:6.503,97.233:0.000,97.249:0.000,97.514:0.200,97.655:0.342,100.003:2.690&vps=96.651:B,96.904:PL,97.233:S,97.249:B,97.514:PL,100.003:PL&bwe=97.233:8470196,100.003:8470196&bat=97.233:1:1,100.003:1:1&bh=97.233:46.080,100.003:43.491&qclc=ChA0RWhQOEY3ZE1qTXVTbTdwEAo net::ERR_BLOCKED_BY_CLIENT
applyHandler @ VM21078:414
apply @ VM21078:938
apply @ VM21078:938
apply @ VM21078:938
applyHandler @ VM21078:414
applyHandler @ VM21078:414
Kgj @ base.js:1719
XT7 @ base.js:5658
(anonymous) @ base.js:5692
T9.then @ base.js:8589
S79 @ base.js:5692
g.L.reportStats @ base.js:11694
(anonymous) @ base.js:5697
(anonymous) @ base.js:1678
setInterval
g.ch @ base.js:1704
kVZ @ base.js:5697
g.L.A9 @ base.js:11822
g.L.PT @ base.js:11831
g.L.PT @ base.js:10983
dB @ base.js:7186
g.L.playVideo @ base.js:13457
g.L.playVideo @ base.js:10767
g.L.WC @ base.js:12387
LZT @ base.js:6425
g.L.WL7 @ base.js:12382
(anonymous) @ base.js:1678Understand this error
BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
qTl8TLcARco:1 [Report Only] Refused to connect to 'data:text/plain;base64,Cg==' because it violates the following Content Security Policy directive: "default-src 'self' https: blob:". Note that 'connect-src' was not explicitly set, so 'default-src' is used as a fallback.

8BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671
setInterval
(anonymous) @ BandLabMidiEditor.tsx:811
handleKeyDown @ BandLabMidiEditor.tsx:954Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:871 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:871
handleKeyUp @ BandLabMidiEditor.tsx:1087Understand this error
BandLabMidiEditor.tsx:237 === LOOP RESTART - MIDI EDITOR STATE ===
BandLabMidiEditor.tsx:238 Current MIDI Notes:
BandLabMidiEditor.tsx:240   Note 1: Pitch=67, Start=0.000, End=0.500, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_67_laa4pxa1d
BandLabMidiEditor.tsx:240   Note 2: Pitch=72, Start=0.500, End=1.000, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_72_bu3oi7654
BandLabMidiEditor.tsx:240   Note 3: Pitch=71, Start=1.000, End=1.500, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_71_3mdl1ry3p
BandLabMidiEditor.tsx:240   Note 4: Pitch=67, Start=1.500, End=3.500, Duration=2.000, ID=rec_session_1750002393528_ez6dh89uw_67_5ukypetn0
BandLabMidiEditor.tsx:240   Note 5: Pitch=67, Start=4.000, End=4.500, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_67_h7n6yssus
BandLabMidiEditor.tsx:240   Note 6: Pitch=72, Start=4.500, End=5.000, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_72_0fq1keagq
BandLabMidiEditor.tsx:240   Note 7: Pitch=71, Start=5.000, End=5.500, Duration=0.500, ID=rec_session_1750002393528_ez6dh89uw_71_lb5hpe9je
BandLabMidiEditor.tsx:240   Note 8: Pitch=72, Start=5.500, End=7.000, Duration=1.500, ID=rec_session_1750002393528_ez6dh89uw_72_ggv0lvuvl
BandLabMidiEditor.tsx:242 Total Notes: 8
BandLabMidiEditor.tsx:243 === END LOOP RESTART STATE ===
6BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
14BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
VM21078:414 
            
            
           POST https://www.youtube.com/api/stats/qoe?fmt=248&afmt=251&cpn=4EhP8F7dMjMuSm7p&el=embedded&ns=yt&fexp=v1%2C24004644%2C562043%2C26443548%2C53408%2C34656%2C106030%2C18644%2C14869%2C75925%2C26895%2C9252%2C3479%2C690%2C12340%2C23206%2C7703%2C7476%2C2%2C4525%2C20976%2C9563%2C16755%2C2795%2C3415%2C1091%2C8887%2C6114%2C2660%2C2040%2C590%2C1797%2C4313%2C1920%2C679%2C80%2C1550%2C2698%2C2301%2C5877%2C3303%2C1090%2C6781&cl=769383743&seq=11&docid=qTl8TLcARco&ei=eOpOaNf9D4zlxN8PzI_egAE&event=streamingstats&plid=AAY3nik6PcXZ1EDy&cbrand=apple&cbr=Chrome&cbrver=*********&c=WEB_EMBEDDED_PLAYER&cver=1.20250609.22.00&cplayer=UNIPLAYER&cos=Macintosh&cosver=10_15_7&cplatform=DESKTOP&bwe=103.770:8470196,110.003:8470196&bat=103.770:1:1,110.003:1:1&cmt=103.770:0.000,103.785:0.000,104.045:0.206,104.803:0.965,110.003:6.165&bh=103.770:46.080,110.003:40.030&vps=103.770:S,103.785:B,104.045:PL,110.003:PL&qclc=ChA0RWhQOEY3ZE1qTXVTbTdwEAs net::ERR_BLOCKED_BY_CLIENT
applyHandler @ VM21078:414
apply @ VM21078:938
apply @ VM21078:938
apply @ VM21078:938
applyHandler @ VM21078:414
applyHandler @ VM21078:414
Kgj @ base.js:1719
XT7 @ base.js:5658
(anonymous) @ base.js:5692
T9.then @ base.js:8589
S79 @ base.js:5692
g.L.reportStats @ base.js:11694
(anonymous) @ base.js:5697
(anonymous) @ base.js:1678
setInterval
g.ch @ base.js:1704
kVZ @ base.js:5697
g.L.A9 @ base.js:11822
g.L.PT @ base.js:11831
g.L.PT @ base.js:10983
dB @ base.js:7186
g.L.playVideo @ base.js:13457
g.L.playVideo @ base.js:10767
g.L.WC @ base.js:12387
LZT @ base.js:6425
g.L.WL7 @ base.js:12382
(anonymous) @ base.js:1678Understand this error
BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671Understand this error
2BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:237 === LOOP RESTART - MIDI EDITOR STATE ===
BandLabMidiEditor.tsx:238 Current MIDI Notes:
BandLabMidiEditor.tsx:240   Note 1: Pitch=72, Start=0.500, End=1.000, Duration=0.500, ID=rec_session_1750002401286_ap1fnhc6j_72_l64zcq98w
BandLabMidiEditor.tsx:240   Note 2: Pitch=71, Start=1.000, End=1.500, Duration=0.500, ID=rec_session_1750002401286_ap1fnhc6j_71_r16vnbvjn
BandLabMidiEditor.tsx:240   Note 3: Pitch=72, Start=4.500, End=5.000, Duration=0.500, ID=rec_session_1750002401286_ap1fnhc6j_72_j02zk1edf
BandLabMidiEditor.tsx:240   Note 4: Pitch=71, Start=5.000, End=5.500, Duration=0.500, ID=rec_session_1750002401286_ap1fnhc6j_71_9og1trr0g
BandLabMidiEditor.tsx:240   Note 5: Pitch=72, Start=5.500, End=7.250, Duration=1.750, ID=rec_session_1750002401286_ap1fnhc6j_72_9q918ymfr
BandLabMidiEditor.tsx:242 Total Notes: 5
BandLabMidiEditor.tsx:243 === END LOOP RESTART STATE ===
4BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671
setInterval
(anonymous) @ BandLabMidiEditor.tsx:811
handleKeyDown @ BandLabMidiEditor.tsx:954Understand this error
6BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:225 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:225
(anonymous) @ BandLabMidiEditor.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
flushPendingEffects @ react-dom_client.js?v=fbe82bdf:11471
flushSpawnedWork @ react-dom_client.js?v=fbe82bdf:11445
commitRoot @ react-dom_client.js?v=fbe82bdf:11276
commitRootWhenReady @ react-dom_client.js?v=fbe82bdf:10695
performWorkOnRoot @ react-dom_client.js?v=fbe82bdf:10640
performSyncWorkOnRoot @ react-dom_client.js?v=fbe82bdf:11838
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=fbe82bdf:11735
processRootScheduleInMicrotask @ react-dom_client.js?v=fbe82bdf:11757
(anonymous) @ react-dom_client.js?v=fbe82bdf:11852Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671Understand this error
4BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:237 === LOOP RESTART - MIDI EDITOR STATE ===
BandLabMidiEditor.tsx:238 Current MIDI Notes:
BandLabMidiEditor.tsx:240   Note 1: Pitch=67, Start=0.000, End=0.500, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_67_4guo392se
BandLabMidiEditor.tsx:240   Note 2: Pitch=72, Start=0.500, End=1.000, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_72_rvcx2ap9m
BandLabMidiEditor.tsx:240   Note 3: Pitch=71, Start=1.000, End=1.500, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_71_773ghmlgm
BandLabMidiEditor.tsx:240   Note 4: Pitch=67, Start=1.500, End=3.500, Duration=2.000, ID=rec_session_1750002406854_ei2fjvfom_67_q75r32y55
BandLabMidiEditor.tsx:240   Note 5: Pitch=67, Start=4.000, End=4.500, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_67_52pmtowfk
BandLabMidiEditor.tsx:240   Note 6: Pitch=72, Start=4.500, End=5.000, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_72_3r2qr896g
BandLabMidiEditor.tsx:240   Note 7: Pitch=71, Start=5.000, End=5.500, Duration=0.500, ID=rec_session_1750002406854_ei2fjvfom_71_jstylw8xq
BandLabMidiEditor.tsx:240   Note 8: Pitch=72, Start=5.500, End=7.000, Duration=1.500, ID=rec_session_1750002406854_ei2fjvfom_72_wmz2fmgaa
BandLabMidiEditor.tsx:242 Total Notes: 8
BandLabMidiEditor.tsx:243 === END LOOP RESTART STATE ===
BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
3BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
VM21078:414 
            
            
           POST https://www.youtube.com/api/stats/qoe?fmt=248&afmt=251&cpn=4EhP8F7dMjMuSm7p&el=embedded&ns=yt&fexp=v1%2C24004644%2C562043%2C26443548%2C53408%2C34656%2C106030%2C18644%2C14869%2C75925%2C26895%2C9252%2C3479%2C690%2C12340%2C23206%2C7703%2C7476%2C2%2C4525%2C20976%2C9563%2C16755%2C2795%2C3415%2C1091%2C8887%2C6114%2C2660%2C2040%2C590%2C1797%2C4313%2C1920%2C679%2C80%2C1550%2C2698%2C2301%2C5877%2C3303%2C1090%2C6781&cl=769383743&seq=12&docid=qTl8TLcARco&ei=eOpOaNf9D4zlxN8PzI_egAE&event=streamingstats&plid=AAY3nik6PcXZ1EDy&cbrand=apple&cbr=Chrome&cbrver=*********&c=WEB_EMBEDDED_PLAYER&cver=1.20250609.22.00&cplayer=UNIPLAYER&cos=Macintosh&cosver=10_15_7&cplatform=DESKTOP&bwe=110.596:8470196,117.331:8470196,120.004:8470196&bat=110.596:1:1,117.331:1:1,120.004:1:1&bh=110.596:46.080,117.331:46.080,120.004:43.747&vps=110.596:S,110.611:B,110.878:PL,117.331:S,117.344:B,117.606:PL,120.004:PL&qclc=ChA0RWhQOEY3ZE1qTXVTbTdwEAw net::ERR_BLOCKED_BY_CLIENT
applyHandler @ VM21078:414
apply @ VM21078:938
apply @ VM21078:938
apply @ VM21078:938
applyHandler @ VM21078:414
applyHandler @ VM21078:414
Kgj @ base.js:1719
XT7 @ base.js:5658
(anonymous) @ base.js:5692
T9.then @ base.js:8589
S79 @ base.js:5692
g.L.reportStats @ base.js:11694
(anonymous) @ base.js:5697
(anonymous) @ base.js:1678Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
useAudioStore.ts:234 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentRenderForLane @ react-dom_client.js?v=fbe82bdf:3083
forceStoreRerender @ react-dom_client.js?v=fbe82bdf:4773
(anonymous) @ react-dom_client.js?v=fbe82bdf:4759
(anonymous) @ zustand.js?v=fbe82bdf:17
setState @ zustand.js?v=fbe82bdf:17
(anonymous) @ zustand_middleware.js?v=fbe82bdf:376
setCurrentTime @ useAudioStore.ts:234
(anonymous) @ YouTubePlayer.tsx:447Understand this error
4BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:225 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:225
(anonymous) @ BandLabMidiEditor.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
flushPendingEffects @ react-dom_client.js?v=fbe82bdf:11471
flushSpawnedWork @ react-dom_client.js?v=fbe82bdf:11445
commitRoot @ react-dom_client.js?v=fbe82bdf:11276
commitRootWhenReady @ react-dom_client.js?v=fbe82bdf:10695
performWorkOnRoot @ react-dom_client.js?v=fbe82bdf:10640
performSyncWorkOnRoot @ react-dom_client.js?v=fbe82bdf:11838
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=fbe82bdf:11735
processRootScheduleInMicrotask @ react-dom_client.js?v=fbe82bdf:11757
(anonymous) @ react-dom_client.js?v=fbe82bdf:11852Understand this error
5BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:196 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:196
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
flushPendingEffects @ react-dom_client.js?v=fbe82bdf:11471
flushSpawnedWork @ react-dom_client.js?v=fbe82bdf:11445
commitRoot @ react-dom_client.js?v=fbe82bdf:11276
commitRootWhenReady @ react-dom_client.js?v=fbe82bdf:10695
performWorkOnRoot @ react-dom_client.js?v=fbe82bdf:10640
performSyncWorkOnRoot @ react-dom_client.js?v=fbe82bdf:11838
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=fbe82bdf:11735
processRootScheduleInMicrotask @ react-dom_client.js?v=fbe82bdf:11757
(anonymous) @ react-dom_client.js?v=fbe82bdf:11852Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671Understand this error
3BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:671 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:671Understand this error
BandLabMidiEditor.tsx:1313 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
getRootForUpdatedFiber @ react-dom_client.js?v=fbe82bdf:3098
enqueueConcurrentHookUpdate @ react-dom_client.js?v=fbe82bdf:3079
dispatchSetStateInternal @ react-dom_client.js?v=fbe82bdf:5476
dispatchSetState @ react-dom_client.js?v=fbe82bdf:5446
(anonymous) @ BandLabMidiEditor.tsx:1313
react-stack-bottom-frame @ react-dom_client.js?v=fbe82bdf:17732
runWithFiberInDEV @ react-dom_client.js?v=fbe82bdf:1540
commitHookEffectListMount @ react-dom_client.js?v=fbe82bdf:8616
commitHookPassiveMountEffects @ react-dom_client.js?v=fbe82bdf:8674
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10065
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10059
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10162
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=fbe82bdf:10046
commitPassiveMountOnFiber @ react-dom_client.js?v=fbe82bdf:10077
flushPassiveEffects @ react-dom_client.js?v=fbe82bdf:11498
(anonymous) @ react-dom_client.js?v=fbe82bdf:11255
performWorkUntilDeadline @ react-dom_client.js?v=fbe82bdf:36Understand this error
BandLabMidiEditor.tsx:237 === LOOP RESTART - MIDI EDITOR STATE ===
BandLabMidiEditor.tsx:238 Current MIDI Notes:
BandLabMidiEditor.tsx:240   Note 1: Pitch=72, Start=0.500, End=1.000, Duration=0.500, ID=rec_session_1750002414811_5oz7hqgum_72_dfk1jimy6
BandLabMidiEditor.tsx:240   Note 2: Pitch=71, Start=1.000, End=1.500, Duration=0.500, ID=rec_session_1750002414811_5oz7hqgum_71_5q5zptn06
BandLabMidiEditor.tsx:240   Note 3: Pitch=72, Start=4.500, End=5.000, Duration=0.500, ID=rec_session_1750002414811_5oz7hqgum_72_fevh5a2kx
BandLabMidiEditor.tsx:240   Note 4: Pitch=71, Start=5.000, End=5.500, Duration=0.500, ID=rec_session_1750002414811_5oz7hqgum_71_qra90yhjv
BandLabMidiEditor.tsx:240   Note 5: Pitch=72, Start=5.500, End=6.750, Duration=1.250, ID=rec_session_1750002414811_5oz7hqgum_72_vm1jjlvud
BandLabMidiEditor.tsx:242 Total Notes: 5
BandLabMidiEditor.tsx:243 === END LOOP RESTART STATE ===