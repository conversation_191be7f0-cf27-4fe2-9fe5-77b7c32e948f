# Blend Recording Feature Test Instructions

## Overview
The blend recording feature allows users to add new notes during recording without overwriting existing notes. Additionally, even in normal recording mode, overwriting only starts after the user presses at least one note.

## New Features Added

### 1. Blend Recording Mode
- **UI Control**: Green checkmark button next to the recording button
- **Keyboard Shortcut**: Press `B` key to toggle
- **Behavior**: When enabled, new recorded notes are added without removing existing notes

### 2. Delayed Overwrite in Normal Mode
- **Behavior**: Even in normal recording mode, existing notes are only overwritten after the user presses at least one note
- **Purpose**: Prevents accidental deletion of existing notes when recording is enabled but no input is given

## Test Scenarios

### Setup
1. Open the application in your browser (http://127.0.0.1:5173/)
2. Navigate to the MIDI editor
3. Set a reference time by pressing `R`
4. Add some existing notes using the note insertion mode (press `A` and click to add notes)

### Test 1: Normal Recording Mode - Delayed Overwrite
1. Enable recording mode by pressing `Shift+R` (recording button should turn red)
2. Ensure blend recording is OFF (green button should not be active)
3. Start playback and let it run over existing notes WITHOUT pressing any keys
4. **Expected**: Existing notes should NOT be overwritten yet
5. **Console**: Should see "🎵 Skipping overwrite - no notes recorded yet in this session"
6. Now press and hold key `1` for about 1 second
7. **Expected**: 
   - New note should be created
   - Existing notes should now start being overwritten as playback continues
   - Console should show overwrite messages

### Test 2: Blend Recording Mode
1. Enable recording mode by pressing `Shift+R`
2. Enable blend recording by pressing `B` (green button should become active)
3. **Console**: Should see "🎛️ Blend recording ENABLED"
4. Start playback and press various keys (`1`, `2`, `3`, etc.)
5. **Expected**:
   - New notes should be created
   - Existing notes should NOT be overwritten at all
   - Console should show "🎛️ Skipping overwrite - blend recording is enabled"

### Test 3: Loop Restart Behavior (CRITICAL)
1. Enable recording mode by pressing `Shift+R`
2. Ensure blend recording is OFF
3. Start playback and press some keys to record notes (e.g., keys `1`, `2`, `3`)
4. Let the video loop restart (or manually seek back to the beginning)
5. **Expected**:
   - Previously recorded notes should become "permanent" (available for overwriting)
   - A new recording session should start
   - Console should show: "🔄 Loop during recording - finalizing current notes and starting new session"
   - Console should show: "🔄 Started new recording session: session_XXXXX"
6. Now press different keys - the new notes should overwrite the previous ones
7. **Critical**: The system should NOT treat this as the same recording session

### Test 4: Recording Before Reference Time (CRITICAL)
1. Set a reference time by pressing `R` at some point in the video (not at the beginning)
2. Enable recording mode by pressing `Shift+R`
3. Seek to a time BEFORE the reference time
4. Press and hold a key (e.g., key `1`) for about 2 seconds, then release
5. **Expected**:
   - Note should be placed at beat 0 (reference start time), not at a negative beat
   - Note duration should match the actual key press duration (~2 seconds worth of beats)
   - During recording, the note should show the correct growing duration in real-time
   - Note should NOT have an extremely long or incorrect duration
   - Note should be properly added to the editor as a permanent note when released
   - Console should show: "🎵 Recording before reference time (-X.XXX), clamping to beat 0"
   - Console should show: "🎵 Stopping note before reference time: raw=-X.XXX, clamped=Y.YYY, noteStart=0.000, actualPress=-X.XXX"
   - Console should show: "🎵 Stopping recording note: currentBeat=Y.YYY, actualPressTime=-X.XXX, duration=Z.ZZZ"

### Test 5: Recording During Spacebar Seek
1. Enable recording mode and start recording a note (press and hold key `1`)
2. While still holding the key, press spacebar to seek back to the beginning
3. **Expected**:
   - The note being recorded should be finalized with its current duration
   - A new recording session should start
   - Console should show backward movement detection and note finalization

### Test 6: Key Held During Loop Restart (CRITICAL)
1. Enable recording mode by pressing `Shift+R`
2. Start playback and press and hold key `1` (keep holding it)
3. Let the video loop restart while still holding the key
4. **Expected**:
   - The old recording note should be finalized and converted to permanent
   - A NEW recording note should automatically start for the continued key press
   - The new recording note should have correct duration tracking from the loop restart point
   - Console should show: "🔄 Keys held during loop: Digit1"
   - Console should show: "🔄 Restarting recording for held keys: Digit1"
   - Console should show: "🔄 Started new recording note for held key: rec_session_XXXXX"
5. Continue holding the key and observe the duration
6. **Expected**:
   - Duration should grow correctly from the loop restart point
   - When released, the note should have the correct duration for the time held after loop restart
7. **Critical**: The recording should be seamless across loop boundaries

### Test 4: Toggle Between Modes
1. Start with normal recording mode
2. Add some notes by pressing keys
3. Toggle blend recording ON with `B` key
4. Continue recording - existing notes should stop being overwritten
5. Toggle blend recording OFF with `B` key
6. Continue recording - overwriting should resume

## Expected Console Messages

### Blend Recording Toggle
```
🎛️ Blend recording ENABLED
🎛️ Blend recording DISABLED
```

### Loop Restart During Recording
```
🔄 Loop detected - stopping all MIDI notes
🔄 Loop during recording - finalizing current notes and starting new session
🔄 Keys held during loop: Digit1, Digit2
🔄 Finalizing recording note rec_session_XXXXX_60_abc123 with duration X.XXX beats
🔄 Converting recording note rec_session_XXXXX_60_abc123 to permanent note note_XXXXX_def456
🔄 Started new recording session: session_XXXXX_ghi789
```

### Key Held During Loop Restart
```
🔄 Loop during recording - finalizing current notes and starting new session
🔄 Keys held during loop: Digit1
🔄 Finalizing recording note rec_session_XXXXX_60_abc123 with duration X.XXX beats
🔄 Started new recording session: session_XXXXX_ghi789
🔄 Restarting recording for held keys: Digit1
🔄 Restarting recording for key Digit1 (degree 1)
🔄 Started new recording note for held key: rec_session_XXXXX_60_def456
🔄 Ignoring continued key press for Digit1 - was held during loop restart
```

### Recording Before Reference Time
```
🎵 Recording before reference time (-1.234), clamping to beat 0
🎵 Adding recording note: { id: 'rec_session_...', pitch: 60, startTime: 0 }
🎵 Stopping note before reference time: raw=-0.567, clamped=0.000, noteStart=0.000, actualPress=-1.234
🎵 Stopping recording note: rawCurrentBeat=-0.567, actualPressTime=-1.234, duration=0.667
```

**Key Point**: The duration (0.667) represents the actual time elapsed from press (-1.234) to release (-0.567), which is 0.667 beats of actual key press time.

### Backward Movement Detection (Spacebar/Seek)
```
🔄 Backward movement detected: from beat X.XXX to Y.YYY
🔄 Finalizing N recording notes due to backward movement
🔄 Finalizing recording note rec_session_XXXXX_60_abc123 with duration X.XXX beats
🔄 Converting recording note rec_session_XXXXX_60_abc123 to permanent note note_XXXXX_def456
🔄 Started new recording session due to backward movement: session_XXXXX_ghi789
```

### Overwrite Behavior
```
🎵 Skipping overwrite - no notes recorded yet in this session
🎛️ Skipping overwrite - blend recording is enabled
🔄 Continuous overwrite: removing note [noteId] (C4) at beat X.XX-X.XX
```

### Recording Notes
```
🎵 Adding recording note: { id: 'rec_session_...', pitch: 60, startTime: X.XX }
```

## UI Elements

### Blend Recording Button
- **Location**: Next to the recording button in the header
- **Appearance**: Green background when active, with checkmark icon
- **Tooltip**: "Toggle Blend Recording (B) - Add notes without overwriting existing ones"

### Recording Button
- **Behavior**: Unchanged - still toggles recording mode
- **Shortcut**: Still `Shift+R`

## Technical Implementation

### State Variables Added
- `blendRecording: boolean` - Whether blend mode is enabled
- `hasRecordedNotesInSession: boolean` - Whether user has pressed at least one note

### Logic Changes
- Continuous overwrite logic now checks both conditions before removing existing notes
- Recording session tracking resets the `hasRecordedNotesInSession` flag
- Keyboard handler added for `B` key

## Troubleshooting

If the feature doesn't work as expected:

1. **Check console messages** - They should indicate why overwriting is being skipped
2. **Verify recording is enabled** - Red recording button should be active
3. **Check reference time is set** - Press `R` to set reference time
4. **Ensure playback is running** - The feature only works during active playback
5. **Try different keys** - Use keys `1-7` for scale degree input

## Success Criteria

✅ Blend recording mode prevents all overwriting
✅ Normal recording mode only starts overwriting after first note press
✅ **Loop restart creates new recording session** - previously recorded notes become permanent
✅ **New recording session can overwrite previous notes** after user input
✅ **Notes recorded before reference time are clamped to beat 0 with correct duration**
✅ **Notes being recorded during loop restart/seek are finalized with current duration**
✅ **Spacebar seek properly finalizes recording notes**
✅ **Keys held during loop restart continue recording seamlessly** - new recording note automatically started
✅ **Loop restart doesn't break recording continuity** - duration tracking continues correctly
✅ **Audio stops properly for finalized notes during loop restart**
✅ **Key release after loop restart works correctly** - stops audio and clears tracking
✅ UI button correctly shows blend recording state
✅ Keyboard shortcut `B` toggles blend recording
✅ Console messages help debug the behavior
✅ Feature works consistently across recording sessions, loop restarts, and seeks
