import React, { useState, useEffect, useRef } from 'react';
import './MidiEditor.css';
import Piano<PERSON><PERSON><PERSON> from './PianoKeys';
import NoteGrid from './NoteGrid';
import { MidiNote } from '../../types/midi';
import { useAudioStore } from '../../store/useAudioStore';
import Metronome from './Metronome';

interface MidiEditorProps {
  // TODO: Define props for the MIDI editor
  // For example, initial MIDI data, configuration options, etc.
  getCurrentVideoTime?: () => number | undefined; // Function to get current video time in seconds
}

const MidiEditor: React.FC<MidiEditorProps> = ({ getCurrentVideoTime }) => {
  const { isPlaying } = useAudioStore();
  const [currentPlayheadTime, setCurrentPlayheadTime] = useState<number | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const activeOscillatorsRef = useRef<Map<string, { oscillator: OscillatorNode, gain: GainNode }>>(new Map());
  const [bpm, setBpm] = useState<number>(120);
  const [referenceStartTime, setReferenceStartTime] = useState<number | null>(null);
  const [metronomeEnabled, setMetronomeEnabled] = useState<boolean>(false);

  // Helper function to convert MIDI pitch to frequency
  const midiPitchToFrequency = (pitch: number): number => {
    return 440 * Math.pow(2, (pitch - 69) / 12);
  };

  const handleBpmChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newBpm = parseInt(event.target.value, 10);
    if (!isNaN(newBpm) && newBpm > 0) {
      setBpm(newBpm);
    }
  };

  const handleSetReferenceTime = React.useCallback(() => {
    let videoTime = 0;
    if (getCurrentVideoTime) {
      try {
        videoTime = getCurrentVideoTime() ?? 0; // Default to 0 if undefined
      } catch (error) {
        // Error calling getCurrentVideoTime in handleSetReferenceTime
        // videoTime remains 0 or its previously set value if part of a larger state logic
      }
    } else {
      // getCurrentVideoTime prop is not provided. Using 0s as reference. Playback synchronization might not work as expected.
    }
    setReferenceStartTime(videoTime);
    // This referenceStartTime (in seconds of the video) will be used with BPM
    // to calculate the absolute timing for MIDI notes.
    // Initialize AudioContext on user gesture
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
      audioContextRef.current.resume();
    }
  }, [getCurrentVideoTime]); // Added getCurrentVideoTime to useCallback dependencies

  // Effect for 'R' key to set reference time
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key.toLowerCase() === 'r') {
        // Prevent typing 'r' in input fields if any are focused
        if (document.activeElement && (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA')) {
          return;
        }
        event.preventDefault(); // Prevent default 'r' key action (e.g., page reload shortcut)
        handleSetReferenceTime();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleSetReferenceTime]); // Add handleSetReferenceTime to dependency array

  useEffect(() => {
    const updatePlayhead = () => {
      if (getCurrentVideoTime) {
        try {
          const videoTime = getCurrentVideoTime();

          if (typeof videoTime === 'number' && !isNaN(videoTime)) {
            setCurrentPlayheadTime(videoTime);
          }
        } catch (error) {
          // Error calling getCurrentVideoTime in updatePlayhead
          // Optionally, you could stop the animation loop here if errors persist
          // if (animationFrameIdRef.current) cancelAnimationFrame(animationFrameIdRef.current);
        }
      }
      if (isPlaying) {
        animationFrameIdRef.current = requestAnimationFrame(updatePlayhead);
      }
    };

    if (isPlaying) {
      animationFrameIdRef.current = requestAnimationFrame(updatePlayhead);
    } else {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
      // Optionally reset playhead time when paused, or leave it at last known position
      // setCurrentPlayheadTime(null); 
    }

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
    };
  }, [isPlaying, getCurrentVideoTime]);

  // Sample MIDI notes for display
  const sampleMidiNotes: MidiNote[] = [
    { id: 'n1', pitch: 60, startTime: 0, duration: 2 },    // C4, starts at beat 0, 2 beats long
    { id: 'n2', pitch: 62, startTime: 1, duration: 1 },    // D4, starts at beat 1, 1 beat long
    { id: 'n3', pitch: 64, startTime: 2.5, duration: 1.5 }, // E4, starts at beat 2.5, 1.5 beats long
    { id: 'n4', pitch: 67, startTime: 4, duration: 4 },    // G4, starts at beat 4, 4 beats long (whole note)
    { id: 'n5', pitch: 55, startTime: 0.5, duration: 0.5 }, // G3, starts at beat 0.5, 0.5 beats long
    { id: 'n6', pitch: 72, startTime: 8, duration: 2 },    // C5, starts at beat 8, 2 beats long
  ];

  // MIDI Playback Logic
  useEffect(() => {
    // Intentionally left empty for debugging the blank page issue.
    // The original logic for MIDI note playback is temporarily removed.
  }, [currentPlayheadTime, referenceStartTime, bpm, sampleMidiNotes, isPlaying]);

  return (
    <div className="midi-editor-container">
      <div className="midi-controls">
        <div className="control-item">
          <label htmlFor="bpm-input">BPM: </label>
          <input 
            type="number" 
            id="bpm-input" 
            value={bpm} 
            onChange={handleBpmChange} 
            min="1"
          />
        </div>
        <div className="control-item">
          <button onClick={handleSetReferenceTime}>Set Reference Start Time</button>
        </div>
        {referenceStartTime !== null && (
          <div className="control-item">
            <p>Reference Time Set: {referenceStartTime.toFixed(2)}s (video time)</p>
          </div>
        )}
        <div className="control-item">
          <button onClick={() => setMetronomeEnabled(!metronomeEnabled)}>
            {metronomeEnabled ? 'Disable Metronome' : 'Enable Metronome'}
          </button>
        </div>
      </div>
      {/* <h2>MIDI Editor</h2> */}
      {/* <p>Piano Roll and controls will go here.</p> */}
      <div className="midi-editor-layout">
        <PianoKeys startOctave={3} octaveCount={3} />
        {/* <NoteGrid 
          octaveCount={3} 
          measuresCount={16} 
          beatsPerMeasure={4} 
          notes={sampleMidiNotes} 
          pixelsPerBeat={20} 
          pixelsPerPitch={15} 
          lowestDisplayPitch={48} C3, assuming 3 octaves starting from C3 means lowest key is C3 
          bpm={bpm}
          referenceStartTime={referenceStartTime}
          currentPlayheadTime={currentPlayheadTime}
        /> */}
      </div>
      {/* Future elements: note grid, individual notes, timeline, controls */}
      <Metronome
        bpm={bpm}
        isPlaying={isPlaying}
        metronomeEnabled={metronomeEnabled}
        getCurrentVideoTime={getCurrentVideoTime}
        referenceStartTime={referenceStartTime ?? 0}
      />
    </div>
  );
};

export default MidiEditor;
