import React, { useState, useEffect, useRef, useCallback } from 'react';
import './BandLabMidiEditor.css';
import { useAudioStore } from '../../store/useAudioStore';
import { MidiNote } from '../../types/midi';
import BandLab<PERSON><PERSON>Keys, { PianoKeysRef } from './BandLabPianoKeys';
import BandLabNoteGrid from './BandLabNoteGrid';
import BandLabMidiHeader from './BandLabMidiHeader';
import Metronome from './Metronome';
import { audioSynth } from '../../utils/audioSynth';
import { getScaleDegreeMidiNote } from '../../utils/musicTheory';

interface BandLabMidiEditorProps {
  getCurrentVideoTime?: () => number | undefined;
  onNotesChange?: (notes: MidiNote[]) => void;
  onEditorStateChange?: (state: {
    bpm: number;
    referenceStartTime: number | null;
    selectedKey: string;
    selectedMode: 'major' | 'minor';
    zoom: number;
  }) => void;
}

/**
 * BandLabMidiEditor - Advanced MIDI editor with scale degree keyboard shortcuts
 *
 * New Features:
 * - Key signature selection (root note + major/minor mode)
 * - Octave selection for keyboard note playing
 * - Keyboard shortcuts for 14 scale degrees (2 full octaves)
 * - Octave adjustment with + and _ keys
 *
 * Usage:
 * 1. Select a key signature (e.g., G Major, A Minor) using the dropdowns
 * 2. Choose an octave (0-8) for the starting octave of the root note
 * 3. Press keys to play scale degrees:
 *    First octave: 1-7 (1=Root, 2=2nd, 3=3rd, 4=4th, 5=5th, 6=6th, 7=7th)
 *    Second octave: 8,9,0,-,=,],\ (8=Root+octave, 9=2nd+octave, etc.)
 * 4. Use + to increase octave, _ to decrease octave
 *
 * The scale naturally crosses octave boundaries, so G Major octave 5 gives:
 * 1:G5, 2:A5, 3:B5, 4:C6, 5:D6, 6:E6, 7:F#6, 8:G6, 9:A6, 0:B6, -:C7, =:D7, ]:E7, \:F#7
 */

export interface MidiEditorState {
  mode: 'select' | 'addNote' | 'velocity';
  velocity: number;
  notePreview: boolean;
  quantizeValue: string;
  bpm: number | string; // Allow string for temporary empty state during editing
  referenceStartTime: number | null;
  zoom: number;
  octaveRange: { start: number; end: number };
  metronomeEnabled: boolean;
  freeMovement: boolean;
  selectedKey: string; // Root note like 'C', 'G', 'F#', etc.
  selectedMode: 'major' | 'minor';
  selectedOctave: number; // Octave for keyboard note playing
  isRecording: boolean; // Whether recording is currently active (independent of mode)
  recordingStartBeat: number | null; // Beat position where recording started
  lastOverwriteBeat: number | null; // Last beat position that was overwritten
  currentRecordingSessionId: string | null; // Unique ID for the current recording session
  noteHeight: number; // Height of notes in pixels
  stickyPlayhead: boolean; // Whether playhead follows playback
  ultraStickyMode: boolean; // Ultra-sticky mode with frame-by-frame following
  blendRecording: boolean; // Whether to blend new notes with existing ones instead of overwriting
  hasRecordedNotesInSession: boolean; // Whether user has pressed at least one note in current recording session
}

const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
  getCurrentVideoTime,
  onNotesChange,
  onEditorStateChange
}) => {
  const { isPlaying, currentTime } = useAudioStore();



  // Refs for scroll synchronization
  const pianoKeysRef = useRef<PianoKeysRef>(null);

  // MIDI Editor State
  const [editorState, setEditorState] = useState<MidiEditorState>({
    mode: 'select',
    velocity: 100,
    notePreview: true,
    quantizeValue: '1/16',
    bpm: 120,
    referenceStartTime: null,
    zoom: 1,
    octaveRange: { start: 2, end: 6 },
    metronomeEnabled: false,
    freeMovement: false,
    selectedKey: 'C',
    selectedMode: 'major',
    selectedOctave: 4,
    isRecording: false,
    recordingStartBeat: null,
    lastOverwriteBeat: null,
    currentRecordingSessionId: null,
    noteHeight: 20, // Default note height in pixels
    stickyPlayhead: true, // Enable sticky playhead by default
    ultraStickyMode: false, // Ultra-sticky mode disabled by default
    blendRecording: false, // Blend recording disabled by default
    hasRecordedNotesInSession: false // No notes recorded initially
  });

  // MIDI notes state - initialize with default samples
  const [midiNotes, setMidiNotes] = useState<MidiNote[]>([
    { id: 'n1', pitch: 60, startTime: 0, duration: 1, velocity: 80 },    // C4
    { id: 'n2', pitch: 64, startTime: 1, duration: 1, velocity: 90 },    // E4
    { id: 'n3', pitch: 67, startTime: 2, duration: 2, velocity: 85 },    // G4
    { id: 'n4', pitch: 72, startTime: 4, duration: 1, velocity: 95 },    // C5
  ]);

  // Keep a ref to the current notes for use in effects that shouldn't re-run when notes change
  const midiNotesRef = useRef(midiNotes);
  useEffect(() => {
    midiNotesRef.current = midiNotes;
  }, [midiNotes]);

  // Selected notes state
  const [selectedNotes, setSelectedNotes] = useState<Set<string | number>>(new Set());



  const [currentPlayheadTime, setCurrentPlayheadTime] = useState<number | null>(null);

  // Track which notes are currently playing to avoid retriggering
  const playingNotesRef = useRef<Set<string | number>>(new Set());
  const lastPlayheadTimeRef = useRef<number | null>(null);

  // Track which scale degree keys are currently pressed to prevent keyboard repeat
  const pressedScaleKeysRef = useRef<Set<string>>(new Set());
  const playingScaleNotesRef = useRef<Map<number, number>>(new Map()); // degree -> midiNote

  // Recording state tracking
  const recordingNotesRef = useRef<Map<number, { startTime: number; actualPressTime: number; noteId: string | number }>>(new Map()); // midiNote -> recording info
  const recordingUpdateIntervalRef = useRef<number | null>(null);

  // Flag to prevent note playback immediately after a loop
  const justLoopedRef = useRef<boolean>(false);

  // Track keys that were pressed before a loop restart to avoid treating them as new input
  const keysHeldDuringLoopRef = useRef<Set<string>>(new Set());

  // Helper function to get numeric BPM value
  const getNumericBpm = useCallback(() => {
    return typeof editorState.bpm === 'number' ? editorState.bpm : 120;
  }, [editorState.bpm]);

  // Utility function to quantize time values (consistent with grid)
  const quantizeTime = useCallback((time: number) => {
    // Handle special case for "free" mode
    if (editorState.quantizeValue === 'free') {
      return time; // No quantization in free mode
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return time; // Invalid format, no quantization
    }

    return Math.round(time / beatSubdivision) * beatSubdivision;
  }, [editorState.quantizeValue]);

  // Notify parent component when notes change
  // Use a ref to track if the change is internal (to prevent circular updates)
  const isInternalUpdateRef = useRef(false);
  const onNotesChangeRef = useRef(onNotesChange);
  onNotesChangeRef.current = onNotesChange;

  // Track previous notes to prevent infinite loops
  const previousNotesRef = useRef<MidiNote[]>([]);

  // Throttle notes change notifications to prevent excessive calls
  const notesChangeTimeoutRef = useRef<number | null>(null);
  const lastNotificationTimeRef = useRef<number>(0);

  useEffect(() => {
    // Only notify parent if this is an internal change (user action)
    // Skip notification if the change came from the parent (external update)
    // Also skip if notes haven't actually changed (prevent infinite loops)
    const notesChanged = JSON.stringify(midiNotes) !== JSON.stringify(previousNotesRef.current);

    if (onNotesChangeRef.current && !isInternalUpdateRef.current && notesChanged) {
      previousNotesRef.current = [...midiNotes]; // Store a copy

      const now = Date.now();
      const timeSinceLastNotification = now - lastNotificationTimeRef.current;

      // Clear any pending notification
      if (notesChangeTimeoutRef.current) {
        clearTimeout(notesChangeTimeoutRef.current);
      }

      // For immediate updates (like note additions/deletions), or if enough time has passed
      if (timeSinceLastNotification > 50 || !editorState.isRecording) {
        // Immediate update for non-recording changes or if enough time has passed
        onNotesChangeRef.current(midiNotes);
        lastNotificationTimeRef.current = now;
      } else {
        // Throttle rapid updates during recording
        notesChangeTimeoutRef.current = setTimeout(() => {
          onNotesChangeRef.current?.(midiNotes);
          lastNotificationTimeRef.current = Date.now();
          notesChangeTimeoutRef.current = null;
        }, 16); // ~60fps for responsive visualization
      }
    }
    // Reset the flag after processing
    isInternalUpdateRef.current = false;

    return () => {
      if (notesChangeTimeoutRef.current) {
        clearTimeout(notesChangeTimeoutRef.current);
      }
    };
  }, [midiNotes, editorState.isRecording]);

  // Notify parent component when editor state changes
  const onEditorStateChangeRef = useRef(onEditorStateChange);
  onEditorStateChangeRef.current = onEditorStateChange;

  useEffect(() => {
    if (onEditorStateChangeRef.current) {
      onEditorStateChangeRef.current({
        bpm: getNumericBpm(),
        referenceStartTime: editorState.referenceStartTime,
        selectedKey: editorState.selectedKey,
        selectedMode: editorState.selectedMode,
        zoom: editorState.zoom
      });
    }
  }, [editorState.bpm, editorState.referenceStartTime, editorState.selectedKey, editorState.selectedMode, editorState.zoom, getNumericBpm]);

  // Update playhead position using the same time source as singing visualization
  // Use ref to prevent infinite re-renders and throttle updates
  const lastCurrentTimeRef = useRef<number | null>(null);
  const playheadUpdateTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    // Only update if time has changed significantly (avoid micro-updates)
    if (lastCurrentTimeRef.current === null || Math.abs(currentTime - lastCurrentTimeRef.current) > 0.01) {
      lastCurrentTimeRef.current = currentTime;

      // Clear any pending update
      if (playheadUpdateTimeoutRef.current) {
        clearTimeout(playheadUpdateTimeoutRef.current);
      }

      // Throttle updates to prevent excessive re-renders
      playheadUpdateTimeoutRef.current = setTimeout(() => {
        setCurrentPlayheadTime(currentTime);
        playheadUpdateTimeoutRef.current = null;
      }, 16); // ~60fps
    }

    return () => {
      if (playheadUpdateTimeoutRef.current) {
        clearTimeout(playheadUpdateTimeoutRef.current);
      }
    };
  }, [currentTime]);

  // Memoize the loop event handler to prevent recreating it on every render
  const handleLoopEvent = useCallback(() => {
    // Log complete MIDI editor state when loop restarts
    console.log('=== LOOP RESTART - MIDI EDITOR STATE ===');
    console.log('Current MIDI Notes:');
    midiNotesRef.current.forEach((note, index) => {
      console.log(`  Note ${index + 1}: Pitch=${note.pitch}, Start=${note.startTime.toFixed(3)}, End=${(note.startTime + note.duration).toFixed(3)}, Duration=${note.duration.toFixed(3)}, ID=${note.id}`);
    });
    console.log(`Total Notes: ${midiNotesRef.current.length}`);
    console.log('=== END LOOP RESTART STATE ===');

    // Set flag to prevent immediate note playback after loop
    justLoopedRef.current = true;

    // Use stopAllNotes for a comprehensive stop of all audio
    audioSynth.stopAllNotes();

    // If recording is active, finalize any currently recording notes and start a new session
    if (editorState.isRecording) {
      // Track which keys were held during the loop to avoid treating them as new input
      keysHeldDuringLoopRef.current = new Set(pressedScaleKeysRef.current);

      // First, finalize any currently recording notes with their current duration
      if (recordingNotesRef.current.size > 0) {
        const currentTime = getCurrentVideoTime?.() || 0;
        const rawCurrentBeat = editorState.referenceStartTime !== null
          ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
          : 0;

        // Finalize each recording note and stop its audio
        recordingNotesRef.current.forEach((recordingInfo, midiNote) => {
          // Use RAW current beat (unclamped) for duration calculation
          const duration = Math.max(0.125, rawCurrentBeat - recordingInfo.actualPressTime);

          // Stop the audio for this note
          audioSynth.stopNote(midiNote);

          // Update the note with final duration - batch this update
          setMidiNotes(prev => prev.map(note =>
            note.id === recordingInfo.noteId
              ? { ...note, duration }
              : note
          ));
        });
      }

      // Convert recording notes to regular notes (make them permanent) - batch this update
      setMidiNotes(prev => prev.map(note => {
        if (typeof note.id === 'string' && note.id.startsWith('rec_')) {
          // Convert recording note to regular note
          const newId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          return { ...note, id: newId };
        }
        return note;
      }));

      // Generate a new session ID for the new recording pass
      const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Reset recording session state for new pass
      setEditorState(prev => ({
        ...prev,
        currentRecordingSessionId: newSessionId,
        hasRecordedNotesInSession: false, // Reset - wait for new input before overwriting
        lastOverwriteBeat: null // Reset overwrite tracking
      }));

      // Start new recording notes for keys that are still being held
      // This ensures continuous recording across loop boundaries
      if (keysHeldDuringLoopRef.current.size > 0) {
        // Get the new timeline position (should be at or near beat 0 after loop restart)
        const newCurrentTime = getCurrentVideoTime?.() || 0;
        const newRawCurrentBeat = editorState.referenceStartTime !== null
          ? (newCurrentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
          : 0;
        const newCurrentBeat = Math.max(0, newRawCurrentBeat);

        // Only create new recording notes if we're not too close to beat 0
        // This prevents tiny notes from being created right at loop restart
        const LOOP_RESTART_THRESHOLD = 0.1; // Don't create notes if within 0.1 beats of start

        if (newCurrentBeat > LOOP_RESTART_THRESHOLD) {
          // Collect all new notes to add in a single batch
          const newNotesToAdd: MidiNote[] = [];

          // Restart recording for each held key
          keysHeldDuringLoopRef.current.forEach(keyCode => {
          const scaleDegree = getScaleDegreeFromKey('', keyCode); // Use empty key, rely on keyCode
          if (scaleDegree) {
            const midiNote = getScaleDegreeMidiNote(
              editorState.selectedKey,
              editorState.selectedMode,
              scaleDegree,
              editorState.selectedOctave
            );

            // Quantize the start time
            const quantizedBeat = quantizeTime(newCurrentBeat);

            // Create new note ID for the new session
            const noteId = `rec_${newSessionId}_${midiNote}_${Math.random().toString(36).substring(2, 11)}`;

            // Create the new recording note
            const newNote: MidiNote = {
              id: noteId,
              pitch: midiNote,
              startTime: quantizedBeat,
              duration: 0.125,
              velocity: editorState.velocity
            };

            newNotesToAdd.push(newNote);

            // Track the new recording note
            recordingNotesRef.current.set(midiNote, {
              startTime: quantizedBeat,
              actualPressTime: newRawCurrentBeat, // Use current time as new press time
              noteId: noteId
            });

            // Track in scale degree mapping
            playingScaleNotesRef.current.set(scaleDegree, midiNote);
          }
        });

        // Add all new notes in a single batch update
        if (newNotesToAdd.length > 0) {
          setMidiNotes(prev => [...prev, ...newNotesToAdd]);
        }

          // Restart real-time updates if we have new recording notes
          if (recordingNotesRef.current.size > 0 && !recordingUpdateIntervalRef.current) {
            recordingUpdateIntervalRef.current = setInterval(updateRecordingNoteDurations, 30);
          }
        } else {
          // Too close to loop start - just maintain audio playback without creating recording notes
          // Keep the keys tracked as held but don't create new recording notes yet
          keysHeldDuringLoopRef.current.forEach(keyCode => {
            const scaleDegree = getScaleDegreeFromKey('', keyCode);
            if (scaleDegree) {
              const midiNote = getScaleDegreeMidiNote(
                editorState.selectedKey,
                editorState.selectedMode,
                scaleDegree,
                editorState.selectedOctave
              );

              // Just maintain audio playback and scale degree tracking
              // The recording will start when the user presses the key again or when enough time passes
              playingScaleNotesRef.current.set(scaleDegree, midiNote);
            }
          });
        }
      }
    }

    // Clear all tracking references EXCEPT pressedScaleKeysRef and the new recording notes
    // We need to keep track of which keys are still physically pressed
    playingNotesRef.current.clear();
    // DON'T clear recordingNotesRef if we just added new recording notes for held keys
    if (!editorState.isRecording || keysHeldDuringLoopRef.current.size === 0) {
      recordingNotesRef.current.clear();
    }
    // DON'T clear playingScaleNotesRef if we just added new recording notes
    if (!editorState.isRecording || keysHeldDuringLoopRef.current.size === 0) {
      playingScaleNotesRef.current.clear();
    }
    // DON'T clear pressedScaleKeysRef - user may still be holding keys

    // Stop real-time recording updates if active
    if (recordingUpdateIntervalRef.current) {
      clearInterval(recordingUpdateIntervalRef.current);
      recordingUpdateIntervalRef.current = null;
    }

    // Reset the flag after a short delay to allow normal playback to resume
    setTimeout(() => {
      justLoopedRef.current = false;
    }, 100);
  }, [editorState.isRecording, editorState.referenceStartTime, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.velocity, getCurrentVideoTime, getNumericBpm, quantizeTime]);

  // Listen for loop events to stop currently playing notes
  useEffect(() => {
    // Listen for the youtube-loop event
    window.addEventListener('youtube-loop', handleLoopEvent as EventListener);

    return () => {
      window.removeEventListener('youtube-loop', handleLoopEvent as EventListener);
    };
  }, [handleLoopEvent]); // Only depend on the memoized handler

  // MIDI Playback Logic - automatically play notes when playhead reaches them
  useEffect(() => {
    if (!isPlaying || currentPlayheadTime === null || editorState.referenceStartTime === null || justLoopedRef.current) {
      // Stop all notes when not playing
      if (!isPlaying) {
        playingNotesRef.current.forEach(noteId => {
          const note = midiNotesRef.current.find(n => n.id === noteId);
          if (note) {
            audioSynth.stopNote(note.pitch);
          }
        });
        playingNotesRef.current.clear();
      }

      // IMPORTANT: Even when MIDI playback is disabled, we still need to prevent interference
      // with manual notes. Check if any manual notes should be protected from other systems.
      if (isPlaying && playingScaleNotesRef.current.size > 0) {
        // Video is playing but no reference time set - ensure manual notes are protected
        // by preventing any automatic note stopping for manually played pitches
      }

      lastPlayheadTimeRef.current = currentPlayheadTime;
      return;
    }

    const currentVideoTime = currentPlayheadTime;
    const lastVideoTime = lastPlayheadTimeRef.current;
    lastPlayheadTimeRef.current = currentVideoTime;

    // Convert video time to musical time (beats)
    const elapsedVideoTime = currentVideoTime - editorState.referenceStartTime;
    if (elapsedVideoTime < 0) return; // Before reference start time

    const beatsPerSecond = getNumericBpm() / 60;
    const currentBeat = elapsedVideoTime * beatsPerSecond;

    // Calculate the time range we need to check for note triggers
    const lastBeat = lastVideoTime !== null ?
      Math.max(0, (lastVideoTime - editorState.referenceStartTime) * beatsPerSecond) :
      currentBeat - 0.1; // Small lookback if no previous time

    // Check each note to see if it should start or stop playing
    midiNotesRef.current.forEach(note => {
      const noteStartBeat = note.startTime;
      const noteEndBeat = note.startTime + note.duration;
      const isCurrentlyPlaying = playingNotesRef.current.has(note.id);

      // Skip recording notes - they are handled separately by the recording system
      const isRecordingNote = typeof note.id === 'string' && note.id.startsWith('rec_');
      if (isRecordingNote && editorState.isRecording) {

        return; // Don't interfere with recording notes
      }

      // Check if this pitch is currently being played manually (keyboard input)
      const isManuallyPlaying = Array.from(playingScaleNotesRef.current.values()).includes(note.pitch);

      // Check if note should start playing
      if (!isCurrentlyPlaying &&
          !isManuallyPlaying && // Don't auto-play if manually playing the same pitch
          noteStartBeat >= lastBeat &&
          noteStartBeat <= currentBeat) {

        // DOUBLE CHECK: Make sure we're not interfering with manual notes
        // Check again right before playing to prevent race conditions
        const isStillManuallyPlaying = Array.from(playingScaleNotesRef.current.values()).includes(note.pitch);
        if (isStillManuallyPlaying) {
          return; // Skip this note completely
        }

        // Calculate how long the note should play
        const remainingDuration = Math.max(0.1, noteEndBeat - currentBeat) / beatsPerSecond;
        audioSynth.playNote(
          note.pitch,
          remainingDuration,
          note.velocity || 80,
          { waveform: 'triangle' }
        );

        playingNotesRef.current.add(note.id);
      }

      // Check if note should stop playing (if it has ended)
      if (isCurrentlyPlaying && currentBeat > noteEndBeat && !isManuallyPlaying) {
        audioSynth.stopNote(note.pitch);
        playingNotesRef.current.delete(note.id);
      }
    });

  }, [currentPlayheadTime, editorState.referenceStartTime, editorState.bpm, isPlaying, getNumericBpm]);

  // Set reference time handler using audio store time for consistency
  // Memoize to prevent unnecessary re-renders
  const handleSetReferenceTime = useCallback(() => {
    setEditorState(prev => ({
      ...prev,
      referenceStartTime: currentTime
    }));
  }, [currentTime]);

  // Get movement increment based on free movement mode
  const getMovementIncrement = useCallback(() => {
    if (editorState.freeMovement || editorState.quantizeValue === 'free') {
      return 0.0625; // Small increment for free movement (1/16th of a quarter note)
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return 0.25; // Default to quarter note
    }

    return beatSubdivision;
  }, [editorState.freeMovement, editorState.quantizeValue]);

  // Utility function to quantize duration values (consistent with BandLabMidiNote)
  const quantizeDuration = useCallback((duration: number) => {
    // Handle special case for "free" mode
    if (editorState.freeMovement || editorState.quantizeValue === 'free') {
      return Math.max(0.0625, duration); // Minimum 1/16 note for free movement
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return Math.max(0.25, duration); // Default minimum quarter note
    }

    // Ensure minimum duration is one grid subdivision
    const minDuration = beatSubdivision;
    const quantizedDuration = Math.round(duration / beatSubdivision) * beatSubdivision;
    return Math.max(minDuration, quantizedDuration);
  }, [editorState.quantizeValue, editorState.freeMovement]);

  // Delete selected notes
  const deleteSelectedNotes = useCallback(() => {
    if (selectedNotes.size === 0) return;

    isInternalUpdateRef.current = true;
    setMidiNotes(prev => prev.filter(note => !selectedNotes.has(note.id)));
    setSelectedNotes(new Set());
  }, [selectedNotes]);

  // Move selected notes by time and pitch delta
  const moveSelectedNotes = useCallback((timeDelta: number, pitchDelta: number) => {
    if (selectedNotes.size === 0) return;

    isInternalUpdateRef.current = true;
    setMidiNotes(prev =>
      prev.map(note => {
        if (selectedNotes.has(note.id)) {
          const newStartTime = Math.max(0, note.startTime + timeDelta);
          const newPitch = Math.max(0, Math.min(127, note.pitch + pitchDelta));

          // Play sound when pitch changes
          if (pitchDelta !== 0) {
            audioSynth.playNote(newPitch, 0.2, note.velocity || 80);
          }

          return { ...note, startTime: newStartTime, pitch: newPitch };
        }
        return note;
      })
    );
  }, [selectedNotes]);

  // Change duration of selected notes by snap increment
  const changeDurationBySnap = useCallback((direction: 'increase' | 'decrease') => {
    if (selectedNotes.size === 0) return;

    const snapIncrement = getMovementIncrement();

    isInternalUpdateRef.current = true;
    setMidiNotes(prev =>
      prev.map(note => {
        if (selectedNotes.has(note.id)) {
          const durationDelta = direction === 'increase' ? snapIncrement : -snapIncrement;
          const newDuration = note.duration + durationDelta;

          // Apply quantization and ensure minimum duration
          const finalDuration = quantizeDuration(newDuration);

          return { ...note, duration: finalDuration };
        }
        return note;
      })
    );
  }, [selectedNotes, getMovementIncrement, quantizeDuration]);

  // Track note start times for non-recording mode
  const nonRecordingStartTimesRef = useRef<Map<number, number>>(new Map());

  // Play scale degree note (start playing) - non-recording mode
  const playScaleDegree = useCallback((degree: number) => {
    try {
      // Don't play if already playing this degree
      if (playingScaleNotesRef.current.has(degree)) {
        return;
      }

      const midiNote = getScaleDegreeMidiNote(
        editorState.selectedKey,
        editorState.selectedMode,
        degree,
        editorState.selectedOctave
      );

      // Record start time for duration calculation
      nonRecordingStartTimesRef.current.set(degree, Date.now());

      // Play note indefinitely for immediate audio feedback (same as recording mode)
      audioSynth.playNote(midiNote, undefined, editorState.velocity || 80);

      // Track that this scale degree is playing
      playingScaleNotesRef.current.set(degree, midiNote);
    } catch (error) {
      // Error playing scale degree
    }
  }, [editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.velocity]);

  // Stop scale degree note - non-recording mode
  const stopScaleDegree = useCallback((degree: number) => {
    const midiNote = playingScaleNotesRef.current.get(degree);

    if (midiNote !== undefined) {
      // Calculate the actual duration the key was held (for logging/debugging)
      const startTime = nonRecordingStartTimesRef.current.get(degree);
      if (startTime) {
        nonRecordingStartTimesRef.current.delete(degree);
      }

      // Stop the note properly
      // Stop via audioSynth
      audioSynth.stopNote(midiNote);

      // Remove from tracking immediately
      playingScaleNotesRef.current.delete(degree);
    }
  }, []);

  // Real-time recording duration update function - memoized to prevent re-creation
  const updateRecordingNoteDurations = useCallback(() => {
    if (!editorState.isRecording || recordingNotesRef.current.size === 0) {
      return;
    }

    const currentTime = getCurrentVideoTime?.() || 0;
    const rawCurrentBeat = editorState.referenceStartTime !== null
      ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
      : 0;

    // Clamp to reference start time (beat 0) if before reference time
    const currentBeat = Math.max(0, rawCurrentBeat);

    // Batch all note updates to prevent multiple re-renders
    const noteUpdates: { [key: string]: Partial<MidiNote> } = {};
    let hasUpdates = false;

    // Calculate updates for all recording notes
    for (const [, recordingInfo] of recordingNotesRef.current.entries()) {
      const visualNoteStartTime = recordingInfo.startTime;
      let currentEffectiveDuration;
      const MIN_DURATION = 0.0625;

      if (editorState.quantizeValue !== 'free' && !editorState.freeMovement) {
        // If quantizing, the effective current end of the note is the quantized current beat
        const quantizedCurrentBeat = quantizeTime(currentBeat);
        currentEffectiveDuration = quantizedCurrentBeat - visualNoteStartTime;
      } else {
        // If not quantizing, the effective current end is just the (clamped) current beat
        currentEffectiveDuration = currentBeat - visualNoteStartTime;
      }

      currentEffectiveDuration = Math.max(MIN_DURATION, currentEffectiveDuration);

      // Check if this note needs updating
      const noteIdStr = recordingInfo.noteId.toString();
      const existingDuration = noteUpdates[noteIdStr]?.duration;
      if (!existingDuration || Math.abs(existingDuration - currentEffectiveDuration) > 0.005) {
        noteUpdates[noteIdStr] = { duration: currentEffectiveDuration };
        hasUpdates = true;
      }
    }

    // Apply all updates in a single batch if there are any changes
    if (hasUpdates) {
      setMidiNotes(prev => prev.map(note => {
        const update = noteUpdates[note.id.toString()];
        return update ? { ...note, ...update } : note;
      }));
    }
  }, [editorState.isRecording, editorState.referenceStartTime, editorState.quantizeValue, editorState.freeMovement, getCurrentVideoTime, quantizeTime, getNumericBpm]);

  // Stop real-time updates when recording stops
  useEffect(() => {
    if (!editorState.isRecording) {
      // Stop the update interval when not recording
      if (recordingUpdateIntervalRef.current) {
        clearInterval(recordingUpdateIntervalRef.current);
        recordingUpdateIntervalRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (recordingUpdateIntervalRef.current) {
        clearInterval(recordingUpdateIntervalRef.current);
        recordingUpdateIntervalRef.current = null;
      }
    };
  }, [editorState.isRecording]);

  // Recording functionality
  const startRecordingNote = useCallback((degree: number) => {
    if (!editorState.isRecording) return;

    try {
      const midiNote = getScaleDegreeMidiNote(
        editorState.selectedKey,
        editorState.selectedMode,
        degree,
        editorState.selectedOctave
      );

      // Don't start recording if this note is already being recorded or played
      if (recordingNotesRef.current.has(midiNote) || playingScaleNotesRef.current.has(degree)) {
        return;
      }

      // Mark that user has pressed at least one note in this recording session
      if (!editorState.hasRecordedNotesInSession) {
        setEditorState(prev => ({
          ...prev,
          hasRecordedNotesInSession: true
        }));
      }

      // Get current time for recording
      const currentTime = getCurrentVideoTime?.() || 0;
      const rawCurrentBeat = editorState.referenceStartTime !== null
        ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
        : 0;

      // Clamp to reference start time (beat 0) if recording before reference time
      const currentBeat = Math.max(0, rawCurrentBeat);

      if (rawCurrentBeat < 0) {
        // Recording before reference time, clamping to beat 0
      }

      // Prevent creating tiny notes right after loop restart
      const LOOP_RESTART_THRESHOLD = 0.15; // Don't create notes if within 0.15 beats of start
      if (currentBeat < LOOP_RESTART_THRESHOLD && justLoopedRef.current) {
        // Too close to loop restart - don't create a recording note
        // Just play the audio for immediate feedback
        audioSynth.playNote(midiNote, undefined, editorState.velocity || 80);
        playingScaleNotesRef.current.set(degree, midiNote);
        return;
      }

      // Quantize the start time based on current quantize setting
      const quantizedBeat = quantizeTime(currentBeat);

      // Create a unique note ID that includes the current recording session ID
      const sessionId = editorState.currentRecordingSessionId || 'unknown';
      const noteId = `rec_${sessionId}_${midiNote}_${Math.random().toString(36).substring(2, 11)}`;

      // Create and add the new note directly
      // Note: Continuous overwrite logic handles removing existing notes across all pitches
      // Don't set isInternalUpdateRef for recording - we want the visualization to update immediately
      setMidiNotes(prev => {
        // Create a new note
        const newNote: MidiNote = {
          id: noteId,
          pitch: midiNote,
          startTime: quantizedBeat,
          duration: 0.125, // Minimum duration, will be updated on release
          velocity: editorState.velocity
        };



        return [...prev, newNote];
      });



      // Track this recording note with the same noteId
      // Store both the note start time (clamped) and the actual press time (unclamped) for duration calculation
      recordingNotesRef.current.set(midiNote, {
        startTime: quantizedBeat, // Note position in the timeline (clamped to beat 0)
        actualPressTime: rawCurrentBeat, // Actual time when key was pressed (can be negative)
        noteId: noteId
      });

      // Start real-time updates if this is the first recording note
      if (recordingNotesRef.current.size === 1 && !recordingUpdateIntervalRef.current) {
        // Use a faster update interval for more responsive visual feedback
        recordingUpdateIntervalRef.current = setInterval(updateRecordingNoteDurations, 30);
      }

      // Play the note for audio feedback AND track it for proper stopping
      audioSynth.playNote(midiNote, undefined, editorState.velocity || 80);

      // Track the note in the scale degree mapping for consistent stopping
      playingScaleNotesRef.current.set(degree, midiNote);
    } catch (error) {
      // Error starting recording note
    }
  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.velocity, getCurrentVideoTime, quantizeTime, getNumericBpm]);

  const stopRecordingNote = useCallback((degree: number) => {
    if (!editorState.isRecording) return;

    try {
      const midiNote = getScaleDegreeMidiNote(
        editorState.selectedKey,
        editorState.selectedMode,
        degree,
        editorState.selectedOctave
      );

      const recordingInfo = recordingNotesRef.current.get(midiNote);
      if (!recordingInfo) {
        return;
      }

      // Get current time for recording (release time)
      const releaseVideoTime = getCurrentVideoTime?.() || 0;
      const rawReleaseBeat = editorState.referenceStartTime !== null
        ? (releaseVideoTime - (editorState.referenceStartTime || 0)) * (getNumericBpm() / 60)
        : recordingInfo.startTime + 0.25; // Fallback if no ref time

      // The note's visual start time in the editor (already clamped and quantized from startRecordingNote)
      const visualNoteStartTime = recordingInfo.startTime;

      // The release beat, clamped to not be before the reference start time (beat 0)
      const clampedReleaseBeat = editorState.referenceStartTime !== null
        ? Math.max(0, rawReleaseBeat)
        : rawReleaseBeat;

      let duration; // This will be the final duration used
      const MIN_DURATION = 0.0625; // A small minimum duration (e.g., 64th note at 120bpm is 0.125/2 = 0.0625)

      if (editorState.quantizeValue !== 'free' && !editorState.freeMovement) {
        const quantizedClampedReleaseBeat = quantizeTime(clampedReleaseBeat);
        duration = quantizedClampedReleaseBeat - visualNoteStartTime;
      } else {
        duration = clampedReleaseBeat - visualNoteStartTime;
      }

      duration = Math.max(MIN_DURATION, duration); // Ensure minimum duration and non-negative




      // Update the note with the final duration
      // Don't set isInternalUpdateRef for recording - we want the visualization to update
      setMidiNotes(prev => {
        const updatedNotes = prev.map(note =>
          note.id === recordingInfo.noteId
            ? { ...note, duration }
            : note
        );

        return updatedNotes;
      });



      // Stop audio playback
      audioSynth.stopNote(midiNote);

      // Remove from both recording tracking and scale degree tracking
      recordingNotesRef.current.delete(midiNote);
      playingScaleNotesRef.current.delete(degree);

      // Stop real-time updates if no more notes are being recorded
      if (recordingNotesRef.current.size === 0 && recordingUpdateIntervalRef.current) {
        clearInterval(recordingUpdateIntervalRef.current);
        recordingUpdateIntervalRef.current = null;
      }
    } catch (error) {
      // Error stopping recording note
    }
  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.quantizeValue, editorState.freeMovement, getCurrentVideoTime, quantizeTime, getNumericBpm]);

  // Map keys to scale degrees (moved outside useEffect to avoid duplication)
  const getScaleDegreeFromKey = useCallback((key: string, code: string): number | null => {
    const keyMap: { [key: string]: number } = {
      '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7,
      '8': 8, '9': 9, '0': 10, '-': 11, '=': 12, ']': 13, '\\': 14
    };

    const codeMap: { [code: string]: number } = {
      'Digit1': 1, 'Digit2': 2, 'Digit3': 3, 'Digit4': 4, 'Digit5': 5, 'Digit6': 6, 'Digit7': 7,
      'Digit8': 8, 'Digit9': 9, 'Digit0': 10, 'Minus': 11, 'Equal': 12, 'BracketRight': 13, 'Backslash': 14
    };

    return keyMap[key] || codeMap[code] || null;
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent shortcuts when typing in input fields
      if (document.activeElement &&
          (document.activeElement.tagName === 'INPUT' ||
           document.activeElement.tagName === 'TEXTAREA')) {
        return;
      }

      // Handle octave adjustment with + and _ keys (Shift + = and Shift + -)
      if (event.key === '+' && !event.ctrlKey && !event.metaKey) {
        event.preventDefault();
        setEditorState(prev => ({
          ...prev,
          selectedOctave: Math.min(8, prev.selectedOctave + 1)
        }));
        return;
      }

      if (event.key === '_' && !event.ctrlKey && !event.metaKey) {
        event.preventDefault();
        setEditorState(prev => ({
          ...prev,
          selectedOctave: Math.max(0, prev.selectedOctave - 1)
        }));
        return;
      }

      // Handle scale degree keys (excluding + and _ which are used for octave adjustment)
      const scaleDegree = getScaleDegreeFromKey(event.key, event.code);
      if (scaleDegree && !event.ctrlKey && !event.metaKey && event.key !== '+' && event.key !== '_') {
        // Prevent keyboard repeat - only play if key wasn't already pressed
        if (!pressedScaleKeysRef.current.has(event.code)) {
          event.preventDefault();
          pressedScaleKeysRef.current.add(event.code);

          if (editorState.isRecording) {
            // If recording is active, start recording the note
            startRecordingNote(scaleDegree);
          } else {
            // If not recording, just play the note
            playScaleDegree(scaleDegree);
          }
        } else if (keysHeldDuringLoopRef.current.has(event.code)) {
          // This key was held during a loop restart - don't treat as new input for overwriting
          // But the recording should already have been restarted by the loop handler
          event.preventDefault();
        }
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'r':
          if (event.shiftKey) {
            // Shift+R for toggling recording (independent of mode)
            event.preventDefault();
            setEditorState(prev => ({
              ...prev,
              isRecording: !prev.isRecording
            }));
          } else {
            // R for reference time
            event.preventDefault();
            handleSetReferenceTime();
          }
          break;
        case 'b':
          // B for blend recording toggle
          event.preventDefault();
          setEditorState(prev => {
            const newBlendMode = !prev.blendRecording;
            return {
              ...prev,
              blendRecording: newBlendMode
            };
          });
          break;
        case 'v':
          event.preventDefault();
          setEditorState(prev => ({
            ...prev,
            mode: prev.mode === 'velocity' ? 'select' : 'velocity'
          }));
          break;
        case 'a':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            // TODO: Select all notes
          } else {
            event.preventDefault();
            setEditorState(prev => ({
              ...prev,
              mode: prev.mode === 'addNote' ? 'select' : 'addNote'
            }));
          }
          break;
        case 'delete':
        case 'backspace':
          event.preventDefault();
          deleteSelectedNotes();
          break;
        case 'escape':
          setEditorState(prev => ({ ...prev, mode: 'select' }));
          break;
        case ' ':
          event.preventDefault();
          // TODO: Toggle playback
          break;
        case 'm':
          event.preventDefault();
          setEditorState(prev => ({ ...prev, metronomeEnabled: !prev.metronomeEnabled }));
          break;
        case 'f':
          event.preventDefault();
          setEditorState(prev => {
            if (prev.quantizeValue === 'free' || prev.freeMovement) {
              // Switch to grid mode
              return {
                ...prev,
                freeMovement: false,
                quantizeValue: prev.quantizeValue === 'free' ? '1/16' : prev.quantizeValue
              };
            } else {
              // Switch to free mode via dropdown
              return { ...prev, quantizeValue: 'free' };
            }
          });
          break;
        case 'arrowup':
          event.preventDefault();
          moveSelectedNotes(0, 1); // Move up by 1 semitone
          break;
        case 'arrowdown':
          event.preventDefault();
          moveSelectedNotes(0, -1); // Move down by 1 semitone
          break;
        case 'arrowleft':
          event.preventDefault();
          if (event.shiftKey) {
            changeDurationBySnap('decrease'); // Shift + Left: Decrease duration
          } else {
            moveSelectedNotes(-getMovementIncrement(), 0); // Move left in time
          }
          break;
        case 'arrowright':
          event.preventDefault();
          if (event.shiftKey) {
            changeDurationBySnap('increase'); // Shift + Right: Increase duration
          } else {
            moveSelectedNotes(getMovementIncrement(), 0); // Move right in time
          }
          break;
        case 'u':
          event.preventDefault();
          setEditorState(prev => ({ ...prev, ultraStickyMode: !prev.ultraStickyMode }));
          break;

      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      // Handle scale degree key releases (excluding + and _ which are used for octave adjustment)
      const scaleDegree = getScaleDegreeFromKey(event.key, event.code);
      if (scaleDegree && event.key !== '+' && event.key !== '_' && pressedScaleKeysRef.current.has(event.code)) {
        pressedScaleKeysRef.current.delete(event.code);

        // Clear from held keys tracking when released
        keysHeldDuringLoopRef.current.delete(event.code);

        if (editorState.isRecording) {
          // If recording is active, stop recording the note
          stopRecordingNote(scaleDegree);
        } else {
          // If not recording, just stop playing the note
          stopScaleDegree(scaleDegree);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleSetReferenceTime, moveSelectedNotes, changeDurationBySnap, getMovementIncrement, deleteSelectedNotes, playScaleDegree, stopScaleDegree, getScaleDegreeFromKey, startRecordingNote, stopRecordingNote]);

  // Cleanup effect - stop all notes when component unmounts
  useEffect(() => {
    return () => {
      // Stop all MIDI playback notes
      playingNotesRef.current.forEach(noteId => {
        const note = midiNotes.find(n => n.id === noteId);
        if (note) {
          audioSynth.stopNote(note.pitch);
        }
      });
      playingNotesRef.current.clear();

      // Stop all scale degree notes
      playingScaleNotesRef.current.forEach((midiNote) => {
        audioSynth.stopNote(midiNote);
      });
      playingScaleNotesRef.current.clear();
      pressedScaleKeysRef.current.clear();

      // Clear non-recording start times
      nonRecordingStartTimesRef.current.clear();

      // Clear non-recording start times
      nonRecordingStartTimesRef.current.clear();

      // Stop all recording notes
      recordingNotesRef.current.forEach((_, midiNote) => {
        audioSynth.stopNote(midiNote);
      });
      recordingNotesRef.current.clear();
    };
  }, []);

  // Stop all scale notes when window loses focus (to prevent stuck notes)
  useEffect(() => {
    const handleBlur = () => {
      // Stop all currently playing scale notes
      playingScaleNotesRef.current.forEach((midiNote) => {
        audioSynth.stopNote(midiNote);
      });
      playingScaleNotesRef.current.clear();
      pressedScaleKeysRef.current.clear();
      keysHeldDuringLoopRef.current.clear(); // Clear held keys tracking

      // Stop all recording notes
      recordingNotesRef.current.forEach((_, midiNote) => {
        audioSynth.stopNote(midiNote);
      });
      recordingNotesRef.current.clear();

      // Stop real-time updates
      if (recordingUpdateIntervalRef.current) {
        clearInterval(recordingUpdateIntervalRef.current);
        recordingUpdateIntervalRef.current = null;
      }
    };

    window.addEventListener('blur', handleBlur);
    return () => window.removeEventListener('blur', handleBlur);
  }, []);

  // Clean up recording notes when recording is disabled
  useEffect(() => {
    if (!editorState.isRecording) {
      // Stop all recording notes when recording is turned off
      recordingNotesRef.current.forEach((_, midiNote) => {
        audioSynth.stopNote(midiNote);
      });
      recordingNotesRef.current.clear();

      // DON'T clear all scale degree tracking - only clear recording notes
      // The playingScaleNotesRef should only be cleared for specific notes that were recording
      // This was the bug causing notes to keep playing when keys were released

      // Stop real-time updates
      if (recordingUpdateIntervalRef.current) {
        clearInterval(recordingUpdateIntervalRef.current);
        recordingUpdateIntervalRef.current = null;
      }

      // Convert recording notes to regular notes when recording stops
      // Don't set isInternalUpdateRef - we want the visualization to update
      setMidiNotes(prev => prev.map(note => {
        if (typeof note.id === 'string' && note.id.startsWith('rec_')) {
          // Convert recording note to regular note
          const newId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          return { ...note, id: newId };
        }
        return note;
      }));

      // Reset recording tracking when recording stops
      setEditorState(prev => ({
        ...prev,
        recordingStartBeat: null,
        lastOverwriteBeat: null,
        currentRecordingSessionId: null,
        hasRecordedNotesInSession: false
      }));
    } else {
      // When recording starts, set the initial recording position
      if (editorState.recordingStartBeat === null) {
        const currentTime = getCurrentVideoTime?.() || 0;
        const currentBeat = editorState.referenceStartTime !== null
          ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
          : 0;

        // Generate a unique session ID for this recording session
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        setEditorState(prev => ({
          ...prev,
          recordingStartBeat: currentBeat,
          lastOverwriteBeat: null, // Start with null to allow immediate overwrite
          currentRecordingSessionId: sessionId,
          hasRecordedNotesInSession: false // Reset flag when recording starts
        }));
      }
    }
  }, [editorState.isRecording, editorState.referenceStartTime, editorState.bpm, getCurrentVideoTime, getNumericBpm]);

  // Memoize the overwrite logic to prevent constant re-creation
  const performOverwrite = useCallback(() => {
    if (!editorState.isRecording || !isPlaying || editorState.referenceStartTime === null) {
      return;
    }

    // Skip overwriting entirely if blend recording is enabled
    if (editorState.blendRecording) {
      return;
    }

    // Only start overwriting after user has pressed at least one note
    if (!editorState.hasRecordedNotesInSession) {
      return;
    }

    const currentTime = getCurrentVideoTime?.() || 0;
    const currentBeat = (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60);

    // Detect if playback has moved backward (loop restart, seek, etc.)
    const hasMovedBackward = editorState.lastOverwriteBeat !== null && currentBeat < editorState.lastOverwriteBeat - 0.1;

    if (hasMovedBackward) {
      // Finalize any currently recording notes before resetting
      if (recordingNotesRef.current.size > 0) {
        // Batch all note updates to prevent multiple re-renders
        const noteUpdates: { [key: string]: Partial<MidiNote> } = {};
        const notesToConvert: string[] = [];

        recordingNotesRef.current.forEach((recordingInfo) => {
          // Use the last overwrite beat as the end time for duration calculation
          const rawLastOverwriteBeat = editorState.lastOverwriteBeat;
          const rawCurrentBeatForDuration = editorState.referenceStartTime !== null
            ? ((getCurrentVideoTime?.() || 0) - editorState.referenceStartTime) * (getNumericBpm() / 60)
            : 0;
          const endBeat = rawLastOverwriteBeat || rawCurrentBeatForDuration;
          const duration = Math.max(0.125, endBeat - recordingInfo.actualPressTime);

          noteUpdates[recordingInfo.noteId.toString()] = { duration };
          if (typeof recordingInfo.noteId === 'string' && recordingInfo.noteId.startsWith('rec_')) {
            notesToConvert.push(recordingInfo.noteId);
          }
        });

        // Single batch update for all note changes
        setMidiNotes(prev => prev.map(note => {
          // Apply duration updates
          const update = noteUpdates[note.id.toString()];
          let updatedNote = update ? { ...note, ...update } : note;

          // Convert recording notes to permanent notes
          if (notesToConvert.includes(note.id.toString())) {
            const newId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            updatedNote = { ...updatedNote, id: newId };
          }

          return updatedNote;
        }));

        // Clear recording tracking
        recordingNotesRef.current.clear();

        // Generate new session ID for new recording pass
        const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        // Reset recording session state
        setEditorState(prev => ({
          ...prev,
          currentRecordingSessionId: newSessionId,
          hasRecordedNotesInSession: false,
          lastOverwriteBeat: null
        }));
      } else {
        // No recording notes, just reset tracking
        setEditorState(prev => ({
          ...prev,
          lastOverwriteBeat: null
        }));
      }

      return; // Let the next effect cycle handle the overwrite with reset state
    }

    // Only process if we've moved forward in time since the last overwrite
    if (editorState.lastOverwriteBeat !== null && currentBeat <= editorState.lastOverwriteBeat) {
      return;
    }

    // Define the range to overwrite (from last processed beat to current beat)
    const startBeat = editorState.lastOverwriteBeat || currentBeat;
    const endBeat = currentBeat;
    const tolerance = 0.05; // Small tolerance for overlap detection



    // Remove existing notes that fall within the recording range (ALL PITCHES)
    setMidiNotes(prev => {
      const currentSessionId = editorState.currentRecordingSessionId;
      let hasRemovals = false;

      const filteredNotes = prev.filter(existingNote => {
        // Only protect notes that were created during the CURRENT recording session
        const isRecordingNote = existingNote.id.toString().startsWith('rec_');
        const isCurrentSessionNote = isRecordingNote && currentSessionId && existingNote.id.toString().startsWith(`rec_${currentSessionId}_`);

        if (isCurrentSessionNote) {
          return true; // Keep notes from current recording session
        }

        // Check if this note overlaps with the current recording range
        const noteStart = existingNote.startTime;
        const noteEnd = existingNote.startTime + existingNote.duration;

        // A note should be removed if any part of it overlaps with the recording range
        const hasOverlap = (noteStart <= endBeat + tolerance) && (noteEnd >= startBeat - tolerance);

        if (hasOverlap) {

          hasRemovals = true;
          return false; // Remove this note
        }

        return true; // Keep notes that don't overlap
      });

      // Only return new array if we actually removed notes
      return hasRemovals ? filteredNotes : prev;
    });

    // Update the last overwrite position
    setEditorState(prev => ({
      ...prev,
      lastOverwriteBeat: currentBeat
    }));
  }, [editorState.isRecording, editorState.referenceStartTime, editorState.blendRecording, editorState.hasRecordedNotesInSession, editorState.lastOverwriteBeat, editorState.currentRecordingSessionId, isPlaying, getCurrentVideoTime, getNumericBpm]);

  // Use a more responsive effect for continuous overwrite
  useEffect(() => {
    if (!editorState.isRecording || !isPlaying || editorState.referenceStartTime === null) {
      return;
    }

    // Skip overwriting entirely if blend recording is enabled
    if (editorState.blendRecording) {
      return;
    }

    // Only start overwriting after user has pressed at least one note
    if (!editorState.hasRecordedNotesInSession) {
      return;
    }

    // Run the overwrite logic directly but with a small delay to batch updates
    const timeoutId = setTimeout(() => {
      performOverwrite();
    }, 16); // ~60fps for responsive overwriting

    return () => clearTimeout(timeoutId);
  }, [currentTime, performOverwrite, editorState.isRecording, isPlaying, editorState.referenceStartTime, editorState.blendRecording, editorState.hasRecordedNotesInSession]);

  // Update editor state handlers
  const updateEditorState = (updates: Partial<MidiEditorState>) => {
    setEditorState(prev => ({ ...prev, ...updates }));
  };

  const addNote = (note: Omit<MidiNote, 'id'>) => {
    const newNote: MidiNote = {
      ...note,
      id: `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    };
    isInternalUpdateRef.current = true;
    setMidiNotes(prev => [...prev, newNote]);
  };

  const updateNote = (noteId: string | number, updates: Partial<MidiNote>) => {
    isInternalUpdateRef.current = true;
    setMidiNotes(prev =>
      prev.map(note =>
        note.id === noteId ? { ...note, ...updates } : note
      )
    );
  };

  const deleteNote = (noteId: string | number) => {
    isInternalUpdateRef.current = true;
    setMidiNotes(prev => prev.filter(note => note.id !== noteId));
  };

  // Transpose selected notes
  const transposeNotes = (semitones: number) => {
    if (selectedNotes.size === 0) return;

    isInternalUpdateRef.current = true;
    setMidiNotes(prev =>
      prev.map(note => {
        if (selectedNotes.has(note.id)) {
          const newPitch = Math.max(0, Math.min(127, note.pitch + semitones));
          return { ...note, pitch: newPitch };
        }
        return note;
      })
    );
  };



  // Apply legato to selected notes
  const applyLegato = useCallback(() => {
    if (selectedNotes.size < 2) {
      return;
    }

    isInternalUpdateRef.current = true;
    setMidiNotes(prev => {
      // Get selected notes and sort by start time
      const selectedNotesArray = prev.filter(note => selectedNotes.has(note.id));
      const sortedNotes = [...selectedNotesArray].sort((a, b) => a.startTime - b.startTime);

      // Create a map for quick lookup of updated notes
      const updatedNotes = new Map<string, MidiNote>();

      // Apply legato: make each note end exactly when the next note starts
      for (let i = 0; i < sortedNotes.length - 1; i++) {
        const currentNote = sortedNotes[i];
        const nextNote = sortedNotes[i + 1];

        // Calculate new duration to make current note end when next note starts
        const newDuration = nextNote.startTime - currentNote.startTime;

        // Ensure minimum duration (1/16 note = 0.25 beats)
        if (newDuration > 0.25) {
          updatedNotes.set(currentNote.id.toString(), {
            ...currentNote,
            duration: newDuration
          });
        }
      }

      // Return updated notes array
      return prev.map(note => updatedNotes.get(note.id.toString()) || note);
    });
  }, [selectedNotes]);

  // Quantize selected notes to current grid
  const quantizeSelectedNotes = useCallback(() => {
    if (selectedNotes.size === 0) {
      return;
    }

    // Don't quantize in free mode
    if (editorState.freeMovement || editorState.quantizeValue === 'free') {
      return;
    }

    isInternalUpdateRef.current = true;
    setMidiNotes(prev =>
      prev.map(note => {
        if (selectedNotes.has(note.id)) {
          const quantizedStartTime = Math.max(0, quantizeTime(note.startTime));
          return { ...note, startTime: quantizedStartTime };
        }
        return note;
      })
    );
  }, [selectedNotes, editorState.quantizeValue, editorState.freeMovement, quantizeTime]);



  return (
    <div className="bandlab-midi-editor">
      {/* Header with close button */}
      <div className="midi-editor-panel-header">
        <div className="midi-editor-track-header">
          <div className="midi-editor-track-icon">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <path fill="currentColor" fillRule="evenodd" d="M4 19h16v3h2v-8.06a4 4 0 0 0-4-4h-2.48l-1.87-5.28A4 4 0 0 0 9.88 2H6a4 4 0 0 0-4 4v16h2zM4 6c0-1.1.9-2 2-2h3.88a2 2 0 0 1 1.89 1.33l2.34 6.61H18a2 2 0 0 1 2 2V17H4z" clipRule="evenodd"/>
              <path fill="currentColor" d="M6 23h12v-2H6z"/>
            </svg>
          </div>
          <div className="midi-editor-track-title">MIDI Instrument</div>
          <div className="midi-editor-header-controls">
            <div className="octave-range-controls">
              <div className="octave-range-buttons">
                <div className="range-control-group">
                  <label className="range-control-label">Start:</label>
                  <button
                    className="midi-editor-button compact"
                    onClick={() => setEditorState(prev => ({
                      ...prev,
                      octaveRange: {
                        ...prev.octaveRange,
                        start: Math.max(-1, prev.octaveRange.start - 1)
                      }
                    }))}
                    disabled={editorState.octaveRange.start <= -1}
                    title="Lower start octave"
                  >
                    -
                  </button>
                  <span className="octave-value">C{editorState.octaveRange.start}</span>
                  <button
                    className="midi-editor-button compact"
                    onClick={() => setEditorState(prev => ({
                      ...prev,
                      octaveRange: {
                        ...prev.octaveRange,
                        start: Math.min(prev.octaveRange.end - 1, prev.octaveRange.start + 1)
                      }
                    }))}
                    disabled={editorState.octaveRange.start >= editorState.octaveRange.end - 1}
                    title="Raise start octave"
                  >
                    +
                  </button>
                </div>

                <div className="range-control-group">
                  <label className="range-control-label">End:</label>
                  <button
                    className="midi-editor-button compact"
                    onClick={() => setEditorState(prev => ({
                      ...prev,
                      octaveRange: {
                        ...prev.octaveRange,
                        end: Math.max(prev.octaveRange.start + 1, prev.octaveRange.end - 1)
                      }
                    }))}
                    disabled={editorState.octaveRange.end <= editorState.octaveRange.start + 1}
                    title="Lower end octave"
                  >
                    -
                  </button>
                  <span className="octave-value">C{editorState.octaveRange.end}</span>
                  <button
                    className="midi-editor-button compact"
                    onClick={() => setEditorState(prev => ({
                      ...prev,
                      octaveRange: {
                        ...prev.octaveRange,
                        end: Math.min(9, prev.octaveRange.end + 1)
                      }
                    }))}
                    disabled={editorState.octaveRange.end >= 9}
                    title="Raise end octave"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="octave-presets">
                <button
                  className="midi-editor-button compact preset"
                  onClick={() => setEditorState(prev => ({ ...prev, octaveRange: { start: 3, end: 6 } }))}
                  title="Set range to C3-C6 (common piano range)"
                >
                  C3-C6
                </button>
                <button
                  className="midi-editor-button compact preset"
                  onClick={() => setEditorState(prev => ({ ...prev, octaveRange: { start: 2, end: 7 } }))}
                  title="Set range to C2-C7 (extended range)"
                >
                  C2-C7
                </button>
                <button
                  className="midi-editor-button compact preset"
                  onClick={() => setEditorState(prev => ({ ...prev, octaveRange: { start: 4, end: 5 } }))}
                  title="Set range to C4-C5 (middle octaves)"
                >
                  C4-C5
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <BandLabMidiHeader
        editorState={editorState}
        updateEditorState={updateEditorState}
        onSetReferenceTime={handleSetReferenceTime}
        selectedNotes={selectedNotes}
        onTransposeNotes={transposeNotes}
        onApplyLegato={applyLegato}
        onQuantizeNotes={quantizeSelectedNotes}
      />

      <div className="midi-editor-main">
        <BandLabPianoKeys
          ref={pianoKeysRef}
          octaveRange={editorState.octaveRange}
          notePreview={editorState.notePreview}
        />

        <BandLabNoteGrid
          notes={midiNotes}
          editorState={editorState}
          currentPlayheadTime={currentPlayheadTime}
          onAddNote={addNote}
          onUpdateNote={updateNote}
          onDeleteNote={deleteNote}
          onSelectedNotesChange={setSelectedNotes}
          onScroll={(scrollTop) => {
            // Synchronize piano keys scroll with note grid scroll
            if (pianoKeysRef.current) {
              pianoKeysRef.current.syncScroll(scrollTop);
            }
          }}

        />
      </div>

      {/* Status Bar */}
      <div className="midi-editor-status-bar">
        <div className="status-item">
          <span className="status-label">Mode:</span>
          <span className="status-value">
            {editorState.mode === 'select' && 'Select (ESC)'}
            {editorState.mode === 'addNote' && 'Add Note (A)'}
            {editorState.mode === 'velocity' && 'Velocity (V)'}
          </span>
        </div>
        {editorState.isRecording && (
          <div className="status-item">
            <span className="status-label">🔴</span>
            <span className="status-value">Recording (Shift+R)</span>
          </div>
        )}
        {editorState.referenceStartTime !== null && (
          <div className="status-item">
            <span className="status-label">Sync:</span>
            <span className="status-value">Video @ {editorState.referenceStartTime.toFixed(2)}s</span>
          </div>
        )}
        <div className="status-item">
          <span className="status-label">Shortcuts:</span>
          <span className="status-value">R=Ref, Shift+R=Record, A=Add, V=Velocity, F=Free, Arrows=Move, Shift+Arrows=Duration, Del=Delete, Click ruler to seek</span>
        </div>
      </div>

      {/* Metronome Component */}
      <Metronome
        bpm={getNumericBpm()}
        isPlaying={isPlaying}
        metronomeEnabled={editorState.metronomeEnabled}
        getCurrentVideoTime={getCurrentVideoTime}
        referenceStartTime={editorState.referenceStartTime ?? 0}
      />
    </div>
  );
};

export default BandLabMidiEditor;
