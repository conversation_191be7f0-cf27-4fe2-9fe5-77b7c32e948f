/**
 * BandLabNoteGrid - High-Performance MIDI Grid Component
 *
 * This component implements multiple performance optimization strategies for rendering
 * MIDI grids at various resolutions:
 *
 * 1. **SVG Patterns (Low-Medium Resolution)**: Uses SVG patterns instead of individual
 *    line elements to reduce DOM complexity from thousands of elements to just a few.
 *
 * 2. **Canvas Rendering (High Resolution)**: For ultra-high resolutions (1/32 and above),
 *    switches to Canvas rendering which provides better performance for dense grids.
 *
 * 3. **Hardware Acceleration**: Uses CSS transforms and will-change properties to
 *    leverage GPU acceleration for smooth scrolling and interactions.
 *
 * 4. **Optimized Rendering**: Implements shape-rendering optimizations and vector-effect
 *    properties for crisp, fast-rendering grid lines.
 *
 * Performance Comparison:
 * - Traditional approach (1/32 grid): ~1,000+ DOM elements, slow rendering
 * - Optimized approach (1/32 grid): ~10 DOM elements + Canvas, fast rendering
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import './BandLabNoteGrid.css';
import { MidiNote } from '../../types/midi';
import { MidiEditorState } from './BandLabMidiEditor';
import BandLabMidiNote from './BandLabMidiNote';
import { audioSynth } from '../../utils/audioSynth';
import { useAudioStore } from '../../store/useAudioStore';
import CanvasGrid from './CanvasGrid';
import { useInfiniteMeasures, optimizeLoadedRange } from '../../hooks/useInfiniteMeasures';

interface BandLabNoteGridProps {
  notes: MidiNote[];
  editorState: MidiEditorState;
  currentPlayheadTime: number | null;
  onAddNote: (note: Omit<MidiNote, 'id'>) => void;
  onUpdateNote: (noteId: string | number, updates: Partial<MidiNote>) => void;
  onDeleteNote: (noteId: string | number) => void;
  onSelectedNotesChange: (selectedNotes: Set<string | number>) => void;
  onScroll?: (scrollTop: number) => void;

}

const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
  notes,
  editorState,
  currentPlayheadTime,
  onAddNote,
  onUpdateNote,
  onDeleteNote,
  onSelectedNotesChange,
  onScroll
}) => {
  // Use the same time source as the singing visualization for synchronization
  const { currentTime: audioStoreCurrentTime, setCurrentTime } = useAudioStore();

  // Get access to the YouTube player instance (same as timeline)
  const getYouTubePlayer = () => {
    try {
      // First try to find the iframe directly
      const iframes = document.querySelectorAll('iframe');
      for (let i = 0; i < iframes.length; i++) {
        const iframe = iframes[i];
        if (iframe.src.includes('youtube.com')) {
          const id = iframe.id;
          // @ts-ignore - YT might not be defined in the window object
          if (window.YT && window.YT.get && id) {
            // @ts-ignore
            return window.YT.get(id);
          }
        }
      }

      // If that fails, try to access the player through the window object
      // @ts-ignore - playerRef might be defined by the YouTubePlayer component
      if (window.youtubePlayerRef && typeof window.youtubePlayerRef === 'object') {
        // @ts-ignore
        return window.youtubePlayerRef;
      }

      console.warn('Could not find YouTube player by iframe or window reference');
      return null;
    } catch (error) {
      console.error('Error accessing YouTube player:', error);
      return null;
    }
  };

  // Directly seek the YouTube player to a specific time (same as timeline)
  const seekYouTubePlayer = (time: number) => {
    try {
      console.log(`MIDI Editor requesting seek to ${time}s`);

      // First update our state immediately
      setCurrentTime(time);

      // Then try to seek the actual YouTube player
      const player = getYouTubePlayer();
      if (player && typeof player.seekTo === 'function') {
        player.seekTo(time, true);
        console.log(`Direct seek to YouTube player at ${time}s`);

        // Dispatch a seek confirmed event immediately after telling the player to seek
        // The player component will restart its interval upon receiving this.
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('youtube-seek-confirmed', {
            detail: { time }
          }));
        }, 10);

        return true;
      }

      // If direct access fails, use a fallback method
      // This dispatches a custom event that the YouTubePlayer component can listen for
      const seekEvent = new CustomEvent('youtube-seek', { detail: { time } });
      window.dispatchEvent(seekEvent);
      console.log(`Dispatched youtube-seek event for ${time}s`);

      return true;
    } catch (error) {
      console.error('Error seeking YouTube player:', error);
      return false;
    }
  };
  const gridRef = useRef<HTMLDivElement>(null);
  const rulerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const lastScrollTargetRef = useRef<number>(0);
  const [gridDimensions, setGridDimensions] = useState({ width: 0, height: 0 });
  const [selectedNotes, setSelectedNotes] = useState<Set<string | number>>(new Set());
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);
  const [isCreatingNote, setIsCreatingNote] = useState(false);
  const [selectionRect, setSelectionRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const [noteCreationStart, setNoteCreationStart] = useState<{ x: number; y: number; beat: number; pitch: number } | null>(null);

  // Clear selection rectangle when mode changes
  useEffect(() => {
    setSelectionRect(null);
  }, [editorState.mode]);

  // Grid configuration
  const pixelsPerBeat = 40 * editorState.zoom;
  const pixelsPerSemitone = editorState.noteHeight || 20; // Use configurable note height
  const beatsPerMeasure = 4;
  const totalSemitones = (editorState.octaveRange.end - editorState.octaveRange.start + 1) * 12;

  // Initialize infinite measures system
  const {
    measureState,
    totalBeats,
    gridWidth,
    handleScroll: handleMeasureScroll
  } = useInfiniteMeasures({
    beatsPerMeasure,
    pixelsPerBeat,
    initialVisibleMeasures: 32,
    maxMeasures: 999,
    bufferSize: 8
  });

  // Calculate grid dimensions
  useEffect(() => {
    if (gridRef.current) {
      const height = totalSemitones * pixelsPerSemitone;
      setGridDimensions({ width: gridWidth, height });
    }
  }, [gridWidth, totalSemitones, pixelsPerSemitone]);

  // Notify parent when selected notes change
  useEffect(() => {
    onSelectedNotesChange(selectedNotes);
  }, [selectedNotes, onSelectedNotesChange]);







  // Periodic optimization of loaded range based on note positions
  useEffect(() => {
    const optimizeInterval = setInterval(() => {
      const optimizedRange = optimizeLoadedRange(
        measureState.loadedRange,
        measureState.visibleRange,
        notes,
        beatsPerMeasure,
        measureState.bufferSize,
        measureState.maxMeasures
      );

      // Only update if there's a significant change
      if (
        Math.abs(optimizedRange.start - measureState.loadedRange.start) > measureState.bufferSize ||
        Math.abs(optimizedRange.end - measureState.loadedRange.end) > measureState.bufferSize
      ) {

        // This would require updating the measureState, but we'll keep it simple for now
      }
    }, 5000); // Optimize every 5 seconds

    return () => clearInterval(optimizeInterval);
  }, [measureState, notes, beatsPerMeasure]);

  // Synchronize ruler scroll with grid scroll and notify parent of vertical scroll
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    const ruler = rulerRef.current;

    if (!scrollContainer || !ruler) return;

    const handleScroll = () => {
      // Synchronize horizontal scroll with ruler
      ruler.scrollLeft = scrollContainer.scrollLeft;

      // Update scroll position for Canvas viewport culling
      setScrollPosition({
        x: scrollContainer.scrollLeft,
        y: scrollContainer.scrollTop
      });

      // Handle infinite measures scroll management
      handleMeasureScroll(scrollContainer.scrollLeft, scrollContainer.clientWidth);

      // Notify parent of vertical scroll for piano keys synchronization
      if (onScroll) {
        onScroll(scrollContainer.scrollTop);
      }
    };

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, [onScroll, handleMeasureScroll]);

  // Utility function to quantize time values
  const quantizeTime = useCallback((time: number) => {
    // Handle special case for "free" mode
    if (editorState.quantizeValue === 'free') {
      return time; // No quantization in free mode
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return time; // Invalid format, no quantization
    }

    return Math.floor(time / beatSubdivision) * beatSubdivision;
  }, [editorState.quantizeValue]);

  // Helper function to calculate initial note duration based on quantize setting
  const getInitialNoteDuration = useCallback(() => {
    if (editorState.quantizeValue === 'free') {
      return 0.25; // Quarter note for free mode
    }

    const quantizeFraction = editorState.quantizeValue.split('/');

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      return wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      return (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return 0.25; // Default to quarter note
    }
  }, [editorState.quantizeValue]);

  // Convert pixel position to musical position
  const pixelToMusical = useCallback((x: number, y: number) => {
    const beat = Math.max(0, x / pixelsPerBeat);
    // Calculate which semitone row we're in - use Math.floor to get the row index
    // The y coordinate represents the top of the clicked area, so we don't need offset
    const semitoneFromTop = Math.floor(y / pixelsPerSemitone);
    const highestMidiNote = editorState.octaveRange.end * 12 + 11 + 12; // +12 because MIDI note 60 is C4
    const midiNote = Math.max(0, Math.min(127, highestMidiNote - semitoneFromTop));

    return { beat, midiNote };
  }, [pixelsPerBeat, pixelsPerSemitone, editorState.octaveRange]);

  // Convert musical position to pixel position
  const musicalToPixel = useCallback((beat: number, midiNote: number) => {
    const x = beat * pixelsPerBeat;
    const highestMidiNote = editorState.octaveRange.end * 12 + 11 + 12; // +12 because MIDI note 60 is C4
    const semitoneFromTop = highestMidiNote - midiNote;
    const y = semitoneFromTop * pixelsPerSemitone;

    return { x, y };
  }, [pixelsPerBeat, pixelsPerSemitone, editorState.octaveRange]);

  // Handle measure click to seek playback (for measure numbers)
  const handleMeasureClick = useCallback((measureNumber: number) => {
    // Only allow seeking if reference time is set
    if (editorState.referenceStartTime === null) {
      // Show a user-friendly message
      alert('Please set a reference time first by pressing R while the video is playing at the desired sync point.');
      return;
    }

    // Calculate the beat position for the start of this measure
    const measureStartBeat = measureNumber * beatsPerMeasure;

    // Convert beats to seconds using BPM
    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
    const beatsPerSecond = bpm / 60;
    const measureStartTimeInSeconds = measureStartBeat / beatsPerSecond;

    // Calculate the video time by adding to the reference start time
    const videoTime = editorState.referenceStartTime + measureStartTimeInSeconds;

    // Seek the playback to this time using the same method as timeline
    seekYouTubePlayer(videoTime);
  }, [editorState.referenceStartTime, editorState.bpm, beatsPerMeasure, seekYouTubePlayer]);

  // Handle ruler area click to seek playback (anywhere in the ruler)
  const handleRulerClick = useCallback((e: React.MouseEvent) => {
    // Only allow seeking if reference time is set
    if (editorState.referenceStartTime === null) {
      console.warn('Cannot seek: Reference time not set. Press R to set reference time.');
      return;
    }

    const ruler = rulerRef.current;
    if (!ruler) return;

    // Calculate click position relative to the ruler
    const rect = ruler.getBoundingClientRect();
    const clickX = e.clientX - rect.left + ruler.scrollLeft;

    // Convert pixel position to beat position
    const beat = clickX / pixelsPerBeat;

    // Convert beats to seconds using BPM
    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
    const beatsPerSecond = bpm / 60;
    const timeInSeconds = beat / beatsPerSecond;

    // Calculate the video time by adding to the reference start time
    const videoTime = editorState.referenceStartTime + timeInSeconds;

    console.log(`Ruler clicked at beat ${beat.toFixed(2)}, seeking to video time ${videoTime.toFixed(2)}s`);

    // Seek the playback to this time using the same method as timeline
    seekYouTubePlayer(videoTime);
  }, [editorState.referenceStartTime, editorState.bpm, pixelsPerBeat, seekYouTubePlayer]);

  // Global mouse event listeners for selection drag
  useEffect(() => {
    if (!isDragging || editorState.mode !== 'select') return;

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!dragStart) return;

      const scrollContainer = scrollContainerRef.current;
      const rect = gridRef.current?.getBoundingClientRect();
      if (!scrollContainer || !rect) return;

      const rulerHeight = 24;
      const currentX = e.clientX - rect.left + scrollContainer.scrollLeft;
      const currentY = e.clientY - rect.top + scrollContainer.scrollTop - rulerHeight;

      // Calculate selection rectangle
      const selectionRectBounds = {
        left: Math.min(dragStart.x, currentX),
        top: Math.min(dragStart.y, currentY),
        right: Math.max(dragStart.x, currentX),
        bottom: Math.max(dragStart.y, currentY)
      };

      // Update visual selection rectangle for rendering
      setSelectionRect({
        x: selectionRectBounds.left,
        y: selectionRectBounds.top,
        width: selectionRectBounds.right - selectionRectBounds.left,
        height: selectionRectBounds.bottom - selectionRectBounds.top
      });

      // Find notes within selection rectangle
      const notesInSelection = notes.filter(note => {
        const { x, y } = musicalToPixel(note.startTime, note.pitch);
        const noteWidth = note.duration * pixelsPerBeat;
        const noteHeight = pixelsPerSemitone;

        return (
          x < selectionRectBounds.right &&
          x + noteWidth > selectionRectBounds.left &&
          y < selectionRectBounds.bottom &&
          y + noteHeight > selectionRectBounds.top
        );
      });

      setSelectedNotes(new Set(notesInSelection.map(note => note.id)));
    };

    const handleGlobalMouseUp = () => {
      setIsDragging(false);
      setDragStart(null);
      setSelectionRect(null);

    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, dragStart, editorState.mode, notes, musicalToPixel, pixelsPerBeat, pixelsPerSemitone]);

  // Global mouse event listeners for note creation drag
  useEffect(() => {
    if (!isCreatingNote) return;

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!noteCreationStart) return;

      const scrollContainer = scrollContainerRef.current;
      if (!scrollContainer) return;

      const containerRect = scrollContainer.getBoundingClientRect();

      // Calculate current mouse position
      const currentX = e.clientX - containerRect.left + scrollContainer.scrollLeft;
      const currentY = e.clientY - containerRect.top + scrollContainer.scrollTop;

      // Calculate the duration based on horizontal mouse movement
      const deltaX = currentX - noteCreationStart.x;
      const deltaBeat = deltaX / pixelsPerBeat;

      // Calculate the pitch based on vertical mouse movement
      const { beat: _, midiNote: newPitch } = pixelToMusical(currentX, currentY);

      // Calculate minimum duration based on quantize setting
      const initialDuration = getInitialNoteDuration();
      const minDuration = Math.max(0.125, initialDuration); // Ensure minimum visibility

      // Calculate new duration (initial duration + extension)
      let newDuration = minDuration + Math.max(0, deltaBeat);

      // Quantize the duration if not in free mode
      if (editorState.quantizeValue !== 'free') {
        // Quantize the end time, then calculate duration
        const endTime = noteCreationStart.beat + newDuration;
        const quantizedEndTime = quantizeTime(endTime);
        newDuration = Math.max(minDuration, quantizedEndTime - noteCreationStart.beat);
      }

      // Find the note being created by its position and original pitch
      const createdNote = notes.find(note =>
        Math.abs(note.startTime - noteCreationStart.beat) < 0.01 && note.pitch === noteCreationStart.pitch
      );

      if (createdNote) {
        // Prepare updates object
        const updates: { duration: number; pitch?: number } = { duration: newDuration };

        // Only update pitch if it has changed
        if (newPitch !== createdNote.pitch) {
          updates.pitch = newPitch;
          // Play sound when pitch changes during creation
          audioSynth.playNote(newPitch, 0.2, createdNote.velocity || 80);
        }

        // Update the note with new duration and potentially new pitch
        onUpdateNote(createdNote.id, updates);

        // Update our tracking of the note's pitch for future updates
        if (updates.pitch !== undefined) {
          setNoteCreationStart(prev => prev ? { ...prev, pitch: newPitch } : null);
        }
      }
    };

    const handleGlobalMouseUp = () => {
      setIsCreatingNote(false);
      setNoteCreationStart(null);

    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isCreatingNote, noteCreationStart, pixelsPerBeat, editorState.quantizeValue, quantizeTime, onUpdateNote, notes, getInitialNoteDuration, pixelToMusical]);

  // Handle grid click for adding notes (fallback for simple clicks without drag)
  const handleGridClick = useCallback((_e: React.MouseEvent) => {
    // This is now only used as a fallback - the main note creation happens in mouse down/move/up
    // We'll keep this for compatibility but it should rarely be called in practice
    if (editorState.mode !== 'addNote' || isDragging || isCreatingNote) return;


  }, [editorState.mode, isDragging, isCreatingNote]);

  // Handle mouse down for selection and note creation
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    if (editorState.mode === 'select') {
      const rect = gridRef.current?.getBoundingClientRect();
      if (!rect) return;

      setIsDragging(true);
      const rulerHeight = 24;
      setDragStart({
        x: e.clientX - rect.left + scrollContainer.scrollLeft,
        y: e.clientY - rect.top + scrollContainer.scrollTop - rulerHeight
      });

      // Clear selection if not holding Ctrl/Cmd
      if (!e.ctrlKey && !e.metaKey) {
        setSelectedNotes(new Set());
      }
    } else if (editorState.mode === 'addNote') {
      // Start note creation in addition mode
      const containerRect = scrollContainer.getBoundingClientRect();

      // Calculate click position relative to the scroll container and add scroll offset
      const x = e.clientX - containerRect.left + scrollContainer.scrollLeft;
      const y = e.clientY - containerRect.top + scrollContainer.scrollTop;

      const { beat, midiNote } = pixelToMusical(x, y);

      // Quantize the beat based on current quantize setting
      const quantizedBeat = quantizeTime(beat);

      // Check if there's already a note at this position
      const existingNote = notes.find(note =>
        Math.abs(note.startTime - quantizedBeat) < 0.01 && note.pitch === midiNote
      );

      if (!existingNote) {
        // Calculate initial duration based on quantize setting
        const initialDuration = getInitialNoteDuration();

        // Ensure minimum duration for visibility (32nd note)
        const minDuration = 0.125;
        const finalDuration = Math.max(minDuration, initialDuration);

        const newNote = {
          pitch: midiNote,
          startTime: quantizedBeat,
          duration: finalDuration,
          velocity: editorState.velocity
        };

        onAddNote(newNote);

        // Play the note sound when it's created
        audioSynth.playNote(midiNote, 0.3, newNote.velocity);

        // Set up for duration extension
        setIsCreatingNote(true);
        setNoteCreationStart({
          x,
          y,
          beat: quantizedBeat,
          pitch: midiNote
        });

        // Store the note position for finding it later
        // We'll find the note by its exact position when we need to update it
      }
    }
  }, [editorState.mode, pixelToMusical, quantizeTime, notes, onAddNote, editorState.velocity, getInitialNoteDuration]);

  // Handle mouse move for note duration extension (selection is now handled by global listeners)
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    if (isCreatingNote && noteCreationStart && editorState.mode === 'addNote') {
      // Handle note duration extension and pitch changes for addition mode
      const containerRect = scrollContainer.getBoundingClientRect();

      // Calculate current mouse position
      const currentX = e.clientX - containerRect.left + scrollContainer.scrollLeft;
      const currentY = e.clientY - containerRect.top + scrollContainer.scrollTop;

      // Calculate the duration based on horizontal mouse movement
      const deltaX = currentX - noteCreationStart.x;
      const deltaBeat = deltaX / pixelsPerBeat;

      // Calculate the pitch based on vertical mouse movement
      const { beat: _, midiNote: newPitch } = pixelToMusical(currentX, currentY);

      // Calculate minimum duration based on quantize setting
      const initialDuration = getInitialNoteDuration();
      const minDuration = Math.max(0.125, initialDuration); // Ensure minimum visibility

      // Calculate new duration (initial duration + extension)
      let newDuration = minDuration + Math.max(0, deltaBeat);

      // Quantize the duration if not in free mode
      if (editorState.quantizeValue !== 'free') {
        // Quantize the end time, then calculate duration
        const endTime = noteCreationStart.beat + newDuration;
        const quantizedEndTime = quantizeTime(endTime);
        newDuration = Math.max(minDuration, quantizedEndTime - noteCreationStart.beat);
      }

      // Find the note being created by its position and original pitch
      const createdNote = notes.find(note =>
        Math.abs(note.startTime - noteCreationStart.beat) < 0.01 && note.pitch === noteCreationStart.pitch
      );

      if (createdNote) {
        // Prepare updates object
        const updates: { duration: number; pitch?: number } = { duration: newDuration };

        // Only update pitch if it has changed
        if (newPitch !== createdNote.pitch) {
          updates.pitch = newPitch;
          // Play sound when pitch changes during creation
          audioSynth.playNote(newPitch, 0.2, createdNote.velocity || 80);
        }

        // Update the note with new duration and potentially new pitch
        onUpdateNote(createdNote.id, updates);

        // Update our tracking of the note's pitch for future updates
        if (updates.pitch !== undefined) {
          setNoteCreationStart(prev => prev ? { ...prev, pitch: newPitch } : null);
        }
      }
    }
  }, [isCreatingNote, noteCreationStart, onUpdateNote, quantizeTime, editorState.quantizeValue, getInitialNoteDuration, pixelToMusical, notes, pixelsPerBeat, editorState.mode]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragStart(null);
    setSelectionRect(null); // Clear the selection rectangle

    // Finalize note creation if we were creating a note
    if (isCreatingNote) {
      setIsCreatingNote(false);
      setNoteCreationStart(null);


    }
  }, [isCreatingNote]);

  // Calculate playhead position using the currentPlayheadTime prop
  const getPlayheadPosition = () => {
    if (currentPlayheadTime === null || editorState.referenceStartTime === null) {
      return null;
    }

    const elapsedTime = currentPlayheadTime - editorState.referenceStartTime;
    if (elapsedTime < 0) return null;

    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
    const beatsPerSecond = bpm / 60;
    const elapsedBeats = elapsedTime * beatsPerSecond;
    return elapsedBeats * pixelsPerBeat;
  };

  const playheadX = getPlayheadPosition();

  // Ultra-sticky playhead with frame-by-frame following
  useEffect(() => {
    if (!editorState.ultraStickyMode || !scrollContainerRef.current || playheadX === null) {
      // Cancel any existing animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      return;
    }

    const scrollContainer = scrollContainerRef.current;
    const containerWidth = scrollContainer.clientWidth;

    const updateScroll = () => {
      if (!scrollContainer || !editorState.ultraStickyMode || playheadX === null) return;

      const playheadPosition = playheadX;
      const centerPosition = containerWidth / 2;
      const targetScrollLeft = Math.max(0, playheadPosition - centerPosition);

      // Smooth interpolation for ultra-smooth following
      const currentScrollLeft = scrollContainer.scrollLeft;
      const scrollDiff = targetScrollLeft - currentScrollLeft;
      const smoothingFactor = 0.15; // Adjust for smoothness (0.1 = very smooth, 0.3 = more responsive)

      if (Math.abs(scrollDiff) > 0.5) { // Only update if there's meaningful difference
        const newScrollLeft = currentScrollLeft + (scrollDiff * smoothingFactor);
        scrollContainer.scrollLeft = newScrollLeft;
        lastScrollTargetRef.current = newScrollLeft;
      }

      // Continue the animation loop
      animationFrameRef.current = requestAnimationFrame(updateScroll);
    };

    // Start the animation loop
    animationFrameRef.current = requestAnimationFrame(updateScroll);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [playheadX, editorState.ultraStickyMode]);

  // Regular sticky playhead (fallback when ultra-sticky is disabled)
  useEffect(() => {
    if (editorState.ultraStickyMode || !editorState.stickyPlayhead || !scrollContainerRef.current || playheadX === null) return;

    const scrollContainer = scrollContainerRef.current;
    const containerWidth = scrollContainer.clientWidth;
    const scrollLeft = scrollContainer.scrollLeft;

    // Calculate if playhead is outside the visible area
    const playheadPosition = playheadX;
    const leftEdge = scrollLeft;
    const rightEdge = scrollLeft + containerWidth;

    // Keep playhead in the center third of the visible area
    const centerThird = containerWidth / 3;
    const targetScrollLeft = playheadPosition - centerThird;

    // Only scroll if playhead is getting close to edges or outside visible area
    if (playheadPosition < leftEdge + centerThird || playheadPosition > rightEdge - centerThird) {
      scrollContainer.scrollTo({
        left: Math.max(0, targetScrollLeft),
        behavior: 'smooth'
      });
    }
  }, [playheadX, editorState.stickyPlayhead, editorState.ultraStickyMode]);





  // Determine rendering strategy based on grid resolution
  const shouldUseCanvasGrid = useCallback(() => {
    const quantizeFraction = editorState.quantizeValue.split('/');
    if (editorState.quantizeValue === 'free') return false;

    if (quantizeFraction.length === 2) {
      const denominator = parseInt(quantizeFraction[1]);
      const subdivisionsPerBeat = denominator / 4;
      // Use Canvas for very high resolutions (1/32 and above) to maintain performance
      return subdivisionsPerBeat >= 8;
    }
    return false;
  }, [editorState.quantizeValue]);

  const useCanvasGrid = shouldUseCanvasGrid();

  // Generate measure backgrounds based on loaded range
  const generateMeasureBackgrounds = () => {
    const backgrounds = [];
    const { start, end } = measureState.loadedRange;

    for (let measure = start; measure < end; measure++) {
      const x = measure * beatsPerMeasure * pixelsPerBeat;
      const width = beatsPerMeasure * pixelsPerBeat;
      const isEvenMeasure = measure % 2 === 0;

      backgrounds.push(
        <rect
          key={`measure-bg-${measure}`}
          x={x}
          y={0}
          width={width}
          height={gridDimensions.height}
          fill={isEvenMeasure ? 'rgba(255, 255, 255, 0.03)' : 'transparent'}
          className="measure-background"
        />
      );
    }

    return backgrounds;
  };

  // Optimized grid rendering using patterns and performance techniques
  const generateOptimizedGridPatterns = useCallback(() => {
    // Parse quantize value to determine subdivision
    const quantizeFraction = editorState.quantizeValue.split('/');
    let subdivisionsPerBeat: number;

    if (editorState.quantizeValue === 'free') {
      subdivisionsPerBeat = 1; // Only beat lines in free mode
    } else if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      subdivisionsPerBeat = 1 / (wholeNotes * 4); // Whole notes are less frequent than beats
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const denominator = parseInt(quantizeFraction[1]);
      subdivisionsPerBeat = denominator / 4; // Convert to subdivisions per quarter note
    } else {
      subdivisionsPerBeat = 4; // Default to 16th notes
    }

    // Calculate pattern dimensions
    const beatWidth = pixelsPerBeat;
    const subdivisionWidth = subdivisionsPerBeat > 1 ? beatWidth / subdivisionsPerBeat : beatWidth;
    const measureWidth = beatsPerMeasure * beatWidth;
    const semitoneHeight = pixelsPerSemitone;
    const octaveHeight = 12 * semitoneHeight;

    // For very high subdivisions (1/32 and above), use a more efficient approach
    const isHighResolution = subdivisionsPerBeat >= 8;

    return (
      <defs>
        {/* Optimized subdivision pattern */}
        {!isHighResolution && editorState.quantizeValue !== 'free' && (
          <pattern
            id="subdivision-pattern"
            x="0"
            y="0"
            width={subdivisionWidth}
            height="100%"
            patternUnits="userSpaceOnUse"
          >
            <line
              x1="0"
              y1="0"
              x2="0"
              y2="100%"
              className="grid-line subdivision-line"
            />
          </pattern>
        )}

        {/* Beat pattern */}
        <pattern
          id="beat-pattern"
          x="0"
          y="0"
          width={beatWidth}
          height="100%"
          patternUnits="userSpaceOnUse"
        >
          <line
            x1="0"
            y1="0"
            x2="0"
            y2="100%"
            className="grid-line beat-line"
          />
        </pattern>

        {/* Measure pattern */}
        <pattern
          id="measure-pattern"
          x="0"
          y="0"
          width={measureWidth}
          height="100%"
          patternUnits="userSpaceOnUse"
        >
          <line
            x1="0"
            y1="0"
            x2="0"
            y2="100%"
            className="grid-line measure-line"
          />
        </pattern>

        {/* Semitone pattern */}
        <pattern
          id="semitone-pattern"
          x="0"
          y="0"
          width="100%"
          height={semitoneHeight}
          patternUnits="userSpaceOnUse"
        >
          <line
            x1="0"
            y1="0"
            x2="100%"
            y2="0"
            className="grid-line semitone-line"
          />
        </pattern>

        {/* Octave pattern */}
        <pattern
          id="octave-pattern"
          x="0"
          y="0"
          width="100%"
          height={octaveHeight}
          patternUnits="userSpaceOnUse"
        >
          <line
            x1="0"
            y1="0"
            x2="100%"
            y2="0"
            className="grid-line octave-line"
          />
        </pattern>

        {/* High-resolution subdivision pattern using CSS background approach */}
        {isHighResolution && (
          <pattern
            id="high-res-subdivision-pattern"
            x="0"
            y="0"
            width={beatWidth}
            height="100%"
            patternUnits="userSpaceOnUse"
          >
            {/* Create subdivision lines within a beat */}
            {Array.from({ length: Math.floor(subdivisionsPerBeat) }, (_, i) => (
              <line
                key={i}
                x1={i * (beatWidth / subdivisionsPerBeat)}
                y1="0"
                x2={i * (beatWidth / subdivisionsPerBeat)}
                y2="100%"
                className="grid-line subdivision-line"
                style={{ opacity: 0.3 }} // Reduce opacity for high-res grids
              />
            ))}
          </pattern>
        )}
      </defs>
    );
  }, [editorState.quantizeValue, pixelsPerBeat, pixelsPerSemitone, beatsPerMeasure]);

  // Generate optimized grid using patterns instead of individual lines
  const generateOptimizedGrid = useCallback(() => {
    // Parse quantize value to determine if we're in high-resolution mode
    const quantizeFraction = editorState.quantizeValue.split('/');
    let subdivisionsPerBeat = 1;

    if (editorState.quantizeValue !== 'free' && quantizeFraction.length === 2) {
      const denominator = parseInt(quantizeFraction[1]);
      subdivisionsPerBeat = denominator / 4;
    }

    const isHighResolution = subdivisionsPerBeat >= 8;

    return (
      <>
        {/* Horizontal grid (semitones and octaves) */}
        <rect
          width="100%"
          height="100%"
          fill="url(#semitone-pattern)"
        />
        <rect
          width="100%"
          height="100%"
          fill="url(#octave-pattern)"
        />

        {/* Vertical grid (subdivisions, beats, and measures) */}
        {editorState.quantizeValue !== 'free' && !isHighResolution && (
          <rect
            width="100%"
            height="100%"
            fill="url(#subdivision-pattern)"
          />
        )}

        {/* High-resolution subdivision pattern for 1/32 and above */}
        {isHighResolution && (
          <rect
            width="100%"
            height="100%"
            fill="url(#high-res-subdivision-pattern)"
          />
        )}

        <rect
          width="100%"
          height="100%"
          fill="url(#beat-pattern)"
        />
        <rect
          width="100%"
          height="100%"
          fill="url(#measure-pattern)"
        />
      </>
    );
  }, [editorState.quantizeValue]);

  return (
    <div
      className={`bandlab-note-grid ${editorState.mode}-mode`}
      ref={gridRef}
    >
      {/* Fixed Ruler at the top - Optimized for high performance */}
      <div className="grid-ruler-container">
        <div className="grid-ruler" ref={rulerRef}>
          <div
            className="ruler-content"
            style={{
              width: gridDimensions.width || totalBeats * pixelsPerBeat,
              position: 'relative',
              // Add measure line background pattern for visual consistency
              backgroundImage: `linear-gradient(to right, #555 1px, transparent 1px)`,
              backgroundSize: `${beatsPerMeasure * pixelsPerBeat}px 100%`,
              backgroundPosition: '0 0',
              cursor: editorState.referenceStartTime !== null ? 'pointer' : 'default'
            }}
            onClick={handleRulerClick}
            title={editorState.referenceStartTime !== null
              ? 'Click anywhere to seek playback to that position'
              : 'Set reference time (R) to enable timeline seeking'
            }
          >
            {/* Optimized measure numbers - only render loaded ones */}
            {(() => {
              const measures = [];
              const measureWidth = beatsPerMeasure * pixelsPerBeat;
              const { start, end } = measureState.loadedRange;

              // Render measures in the loaded range with reasonable limit for performance
              const maxMeasuresToRender = Math.min(end - start, 100); // Limit to reasonable number

              for (let i = 0; i < maxMeasuresToRender; i++) {
                const measureNumber = start + i;
                measures.push(
                  <div
                    key={`measure-${measureNumber}`}
                    className="ruler-mark measure-mark"
                    style={{
                      left: measureNumber * measureWidth,
                      cursor: editorState.referenceStartTime !== null ? 'pointer' : 'default'
                    }}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent ruler click handler from firing
                      handleMeasureClick(measureNumber);
                    }}
                    title={editorState.referenceStartTime !== null
                      ? `Click to seek to measure ${measureNumber + 1}`
                      : 'Set reference time (R) to enable measure seeking'
                    }
                  >
                    {measureNumber + 1}
                  </div>
                );
              }

              return measures;
            })()}

            {/* Note: Subdivision marks removed per user request - keeping only measure numbers */}

            {/* Grid info indicator */}
            <div style={{
              position: 'absolute',
              top: '2px',
              right: '5px',
              fontSize: '10px',
              color: '#00C37D',
              background: 'rgba(0,0,0,0.5)',
              padding: '2px 4px',
              borderRadius: '2px'
            }}>
              Grid: {editorState.quantizeValue}
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable content area */}
      <div className="grid-scroll-container" ref={scrollContainerRef}>
        {/* Debug indicator for scroll area and infinite measures */}
        <div style={{
          position: 'absolute',
          top: '5px',
          right: '5px',
          fontSize: '10px',
          color: '#ff6b6b',
          background: 'rgba(0,0,0,0.7)',
          padding: '2px 4px',
          borderRadius: '2px',
          zIndex: 100
        }}>
          Notes: {notes.length} | Measures: {measureState.loadedRange.start + 1}-{measureState.loadedRange.end} | Max: {measureState.maxMeasures}
        </div>

        {/* Infinite scroll indicator */}
        <div style={{
          position: 'absolute',
          top: '25px',
          right: '5px',
          fontSize: '10px',
          color: '#00C37D',
          background: 'rgba(0,0,0,0.7)',
          padding: '2px 4px',
          borderRadius: '2px',
          zIndex: 100
        }}>
          Infinite Grid: {measureState.loadedRange.end < measureState.maxMeasures ? 'Active' : 'At Limit'}
        </div>

        {/* Test buttons for infinite scroll */}
        <div style={{
          position: 'absolute',
          top: '45px',
          right: '5px',
          fontSize: '10px',
          zIndex: 100,
          display: 'flex',
          gap: '4px'
        }}>
          <button
            style={{
              padding: '2px 4px',
              fontSize: '10px',
              background: '#333',
              color: '#fff',
              border: '1px solid #555',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            onClick={() => {
              const scrollContainer = scrollContainerRef.current;
              if (scrollContainer) {
                const measureWidth = beatsPerMeasure * pixelsPerBeat;
                scrollContainer.scrollLeft = 50 * measureWidth; // Scroll to measure 50
              }
            }}
          >
            Go to M50
          </button>
          <button
            style={{
              padding: '2px 4px',
              fontSize: '10px',
              background: '#333',
              color: '#fff',
              border: '1px solid #555',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            onClick={() => {
              const scrollContainer = scrollContainerRef.current;
              if (scrollContainer) {
                const measureWidth = beatsPerMeasure * pixelsPerBeat;
                scrollContainer.scrollLeft = 100 * measureWidth; // Scroll to measure 100
              }
            }}
          >
            Go to M100
          </button>
          <button
            style={{
              padding: '2px 4px',
              fontSize: '10px',
              background: '#333',
              color: '#fff',
              border: '1px solid #555',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
            onClick={() => {
              const scrollContainer = scrollContainerRef.current;
              if (scrollContainer) {
                scrollContainer.scrollLeft = 0; // Scroll back to start
              }
            }}
          >
            Go to Start
          </button>
        </div>
        {/* Conditional rendering: Canvas for ultra-high resolution, SVG for normal resolution */}
        {useCanvasGrid ? (
          <>
            {/* Canvas-based grid for high performance */}
            <CanvasGrid
              width={gridDimensions.width || totalBeats * pixelsPerBeat}
              height={gridDimensions.height || totalSemitones * pixelsPerSemitone}
              pixelsPerBeat={pixelsPerBeat}
              pixelsPerSemitone={pixelsPerSemitone}
              beatsPerMeasure={beatsPerMeasure}
              totalBeats={totalBeats}
              totalSemitones={totalSemitones}
              quantizeValue={editorState.quantizeValue}
              className="grid-canvas"
              viewportX={scrollPosition.x}
              viewportY={scrollPosition.y}
              viewportWidth={gridDimensions.width || 800}
              viewportHeight={gridDimensions.height || 600}
            />
            {/* Minimal SVG for interactive elements only */}
            <svg
              ref={svgRef}
              className="grid-svg"
              width={gridDimensions.width || totalBeats * pixelsPerBeat}
              height={gridDimensions.height || totalSemitones * pixelsPerSemitone}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onClick={handleGridClick}
              style={{ display: 'block', background: 'transparent' }}
            >
              {/* Measure backgrounds */}
              {generateMeasureBackgrounds()}

              {/* Playhead */}
              {playheadX !== null && (
                <line
                  x1={playheadX}
                  y1={0}
                  x2={playheadX}
                  y2={gridDimensions.height}
                  className="playhead-line"
                />
              )}

              {/* Selection Rectangle */}
              {selectionRect && editorState.mode === 'select' && (
                <rect
                  x={selectionRect.x}
                  y={selectionRect.y}
                  width={selectionRect.width}
                  height={selectionRect.height}
                  fill="rgba(0, 195, 125, 0.1)"
                  stroke="#00C37D"
                  strokeWidth="1"
                  strokeDasharray="4,4"
                  pointerEvents="none"
                />
              )}
            </svg>
          </>
        ) : (
          /* Standard SVG-based grid for normal resolutions */
          <svg
            ref={svgRef}
            className="grid-svg"
            width={gridDimensions.width || totalBeats * pixelsPerBeat}
            height={gridDimensions.height || totalSemitones * pixelsPerSemitone}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onClick={handleGridClick}
            style={{ display: 'block' }}
          >
            {/* Pattern definitions for optimized grid rendering */}
            {generateOptimizedGridPatterns()}

            {/* Background fill */}
            <rect
              width="100%"
              height="100%"
              fill="#1a1a1a"
            />

            {/* Measure backgrounds */}
            {generateMeasureBackgrounds()}

            {/* Optimized grid using patterns */}
            {generateOptimizedGrid()}

            {/* Playhead */}
            {playheadX !== null && (
              <line
                x1={playheadX}
                y1={0}
                x2={playheadX}
                y2={gridDimensions.height}
                className="playhead-line"
              />
            )}

            {/* Selection Rectangle */}
            {selectionRect && editorState.mode === 'select' && (
              <rect
                x={selectionRect.x}
                y={selectionRect.y}
                width={selectionRect.width}
                height={selectionRect.height}
                fill="rgba(0, 195, 125, 0.1)"
                stroke="#00C37D"
                strokeWidth="1"
                strokeDasharray="4,4"
                pointerEvents="none"
              />
            )}
          </svg>
        )}

      {/* MIDI Notes */}
      <div className="notes-container">
        {notes.map(note => (
          <BandLabMidiNote
            key={note.id}
            note={note}
            isSelected={selectedNotes.has(note.id)}
            pixelsPerBeat={pixelsPerBeat}
            pixelsPerSemitone={pixelsPerSemitone}
            editorState={editorState}
            position={musicalToPixel(note.startTime, note.pitch)}
            onUpdate={(updates) => onUpdateNote(note.id, updates)}
            onDelete={() => onDeleteNote(note.id)}
            onSelect={(selected, multiSelect) => {
              if (selected) {
                setSelectedNotes(prev => {
                  if (multiSelect) {
                    // Multi-select: add to existing selection
                    return new Set([...prev, note.id]);
                  } else {
                    // Single select: clear others and select only this note
                    return new Set([note.id]);
                  }
                });
              } else {
                // Deselect this note
                setSelectedNotes(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(note.id);
                  return newSet;
                });
              }
            }}
          />
        ))}
      </div>
      </div> {/* Close grid-scroll-container */}
    </div>
  );
};

export default BandLabNoteGrid;
