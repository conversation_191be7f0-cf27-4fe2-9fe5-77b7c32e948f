import React, { useEffect, useRef, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { useAudioStore } from '../store/useAudioStore';

// YouTube IFrame API type
declare global {
  interface Window {
    YT: {
      Player: new (
        elementId: string | HTMLElement,
        options: {
          videoId: string;
          playerVars?: {
            autoplay?: 0 | 1;
            controls?: 0 | 1;
            disablekb?: 0 | 1;
            enablejsapi?: 0 | 1;
            fs?: 0 | 1;
            modestbranding?: 0 | 1;
            playsinline?: 0 | 1;
            rel?: 0 | 1;
            start?: number;
          };
          events?: {
            onReady?: (event: { target: YT.Player }) => void;
            onStateChange?: (event: { data: number; target: YT.Player }) => void;
            onError?: (event: { data: number }) => void;
            onPlaybackRateChange?: (event: { data: number }) => void;
          };
        }
      ) => YT.Player;
      PlayerState: {
        ENDED: number;
        PLAYING: number;
        PAUSED: number;
        BUFFERING: number;
        CUED: number;
      };
      get: (id: string) => YT.Player;
    };
    onYouTubeIframeAPIReady: () => void;
    youtubePlayerRef: any;
    debugYouTubeInterval: {
      isRunning: boolean;
      lastUpdateTime: number;
      updates: number;
      lastTime: number;
    };
    restartYouTubeInterval: () => void;
    isHoldingEndForLoop?: boolean;
  }
}

namespace YT {
  export interface Player {
    playVideo(): void;
    pauseVideo(): void;
    stopVideo(): void;
    seekTo(seconds: number, allowSeekAhead?: boolean): void;
    getPlayerState(): number;
    getCurrentTime(): number;
    getDuration(): number;
    setPlaybackRate(rate: number): void;
    getPlaybackRate(): number;
    getAvailablePlaybackRates(): number[];
    setVolume(volume: number): void;
    getVolume(): number;
    mute(): void;
    unMute(): void;
    isMuted(): boolean;
    getVideoUrl(): string;
    getVideoData(): { video_id: string };
    addEventListener(event: string, handler: Function): void;
    removeEventListener(event: string, handler: Function): void;
    destroy(): void; // Add destroy method
  }
}

// ---------------------------------------------------------------------------
//  Playback-rate command queue
// ---------------------------------------------------------------------------


// Helper function to extract YouTube video ID from URL
const extractYouTubeId = (url: string): string | null => {
  const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
  const match = url.match(regExp);
  return (match && match[7].length === 11) ? match[7] : null;
};



interface YouTubePlayerProps {
  url: string;
}

export interface YouTubePlayerRef {
  getCurrentTime: () => number | undefined;
  getPlayerState: () => number | undefined;
  // Add other methods you want to expose, e.g., playVideo, pauseVideo
}

const YouTubePlayer = forwardRef<YouTubePlayerRef, YouTubePlayerProps>(({ url }, ref) => {
  // Refs
  const playerRef = useRef<YT.Player | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const debounceTimeoutRef = useRef<number | null>(null);
  const isHandlingSeekEventRef = useRef(false); // Track if we're in the middle of handling a seek event

  // State
  const [isApiReady, setIsApiReady] = useState(false);
  const [videoId, setVideoId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Store
  const {
    isPlaying,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    volume,
    loopActive,
    loopStart,
    loopEnd,
    loopDelay,
    currentTime
  } = useAudioStore();

  /* ------------------------------------------------------------------ *
   *  Keep live refs of loop values so the interval callback always sees
   *  the latest numbers without forcing this component to re‑mount the
   *  YouTube iframe every time the user adjusts the loop handles.
   * ------------------------------------------------------------------ */
  const loopActiveRef = useRef(loopActive);
  const loopStartRef  = useRef(loopStart);
  const loopEndRef    = useRef(loopEnd);
  const loopDelayRef  = useRef(loopDelay);

  useEffect(() => { loopActiveRef.current = loopActive; }, [loopActive]);
  useEffect(() => { loopStartRef.current  = loopStart;  }, [loopStart]);
  useEffect(() => { loopEndRef.current    = loopEnd;    }, [loopEnd]);
  useEffect(() => { loopDelayRef.current  = loopDelay;  }, [loopDelay]);





  // Load YouTube IFrame API
  useEffect(() => {
    if (!window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

      window.onYouTubeIframeAPIReady = () => {
        setIsApiReady(true);
      };
    } else {
      setIsApiReady(true);
    }

    return () => {
      // Cleanup
      if (playerRef.current) {
        playerRef.current.stopVideo();
      }

      // Clear any pending timeouts
      if (debounceTimeoutRef.current) {
        window.clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Extract video ID from URL
  useEffect(() => {
    const id = extractYouTubeId(url);
    setVideoId(id);
    setErrorMessage(null);
  }, [url]);



  // Make the player reference available globally for other components
  useEffect(() => {
    if (playerRef.current) {
      // @ts-ignore - Adding a global reference for the timeline to access
      window.youtubePlayerRef = playerRef.current;
    }

    // Listen for custom seek events from the timeline
    const handleSeekEvent = (e: CustomEvent) => {
      if (playerRef.current && e.detail && typeof e.detail.time === 'number') {
        const seekTime = e.detail.time;

        // Set flag to prevent the external currentTime useEffect from interfering
        isHandlingSeekEventRef.current = true;

        // Update the store immediately
        setCurrentTime(seekTime);

        // Seek the player
        playerRef.current.seekTo(seekTime, true);

        // Reset the flag after a short delay
        setTimeout(() => {
          isHandlingSeekEventRef.current = false;
        }, 100);

        // Dispatch an event back to the timeline to confirm the seek
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('youtube-seek-confirmed', {
            detail: { time: seekTime }
          }));
        }, 10);

        // Restart the time update interval to ensure continuous updates
        setTimeout(() => {
          if (typeof window.restartYouTubeInterval === 'function') {
            window.restartYouTubeInterval();
          }
        }, 20);
      }
    };

    // Add event listener for custom seek events
    window.addEventListener('youtube-seek', handleSeekEvent as EventListener);

    return () => {
      // Clean up global reference and event listener
      // @ts-ignore
      delete window.youtubePlayerRef;
      window.removeEventListener('youtube-seek', handleSeekEvent as EventListener);
    };
  }, [playerRef.current, setCurrentTime]);







  // Initialize player when API and video ID are ready
  useEffect(() => {
    if (isApiReady && videoId && containerRef.current) {
      // Clean up existing player if it exists
      if (playerRef.current) {
        try {
          playerRef.current.stopVideo();
          // @ts-ignore - destroy() exists but is not in the type definition
          if (typeof playerRef.current.destroy === 'function') {
            playerRef.current.destroy();
          }
        } catch (error) {
          // Error destroying existing player
        }
        playerRef.current = null;
      }

      // Clear the container
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }

      // Reset error state for new video
      setErrorMessage(null);

      try {
        // @ts-ignore - Bypassing persistent type error, likely due to conflicting @types/youtube definitions
        playerRef.current = new window.YT.Player(containerRef.current!, {
          videoId,
          playerVars: {
            autoplay: 0,
            controls: 1,
            disablekb: 0,
            enablejsapi: 1,
            fs: 0,
            modestbranding: 1,
            playsinline: 1,
            rel: 0,
            iv_load_policy: 3, // Disable video annotations
            cc_load_policy: 0, // Disable closed captions by default
          },
          events: {
            onReady: (event) => {
              // Set initial duration
              const duration = event.target.getDuration();
              setDuration(duration);

              // Set initial volume
              event.target.setVolume(volume * 100);


            },
            onStateChange: (event) => {
              const newState = event.data;


              // Only update playing state for definitive play/pause states
              // Avoid updating during buffering or seeking states that might be temporary
              if (newState === window.YT.PlayerState.PLAYING) {
                setIsPlaying(true);
              } else if (newState === window.YT.PlayerState.PAUSED) {
                // Add a small delay before setting paused state to allow for seeks
                setTimeout(() => {
                  // Double-check the state hasn't changed back to playing
                  if (playerRef.current && playerRef.current.getPlayerState() === window.YT.PlayerState.PAUSED) {
                    setIsPlaying(false);
                  }
                }, 100);
              } else if (newState === window.YT.PlayerState.ENDED) {
                setIsPlaying(false);
              }
              // Don't update state for BUFFERING, CUED, or other transitional states

              // Update duration if it changed
              const duration = event.target.getDuration();
              setDuration(duration);
            },

            onError: (event) => {
              let errorText = 'An error occurred with the YouTube player.';

              switch (event.data) {
                case 2:
                  errorText = 'Invalid YouTube video ID.';
                  break;
                case 5:
                  errorText = 'The requested content cannot be played in an HTML5 player.';
                  break;
                case 100:
                  errorText = 'The video requested was not found or has been removed.';
                  break;
                case 101:
                case 150:
                  errorText = 'The owner of the requested video does not allow it to be played in embedded players.';
                  break;
              }

              setErrorMessage(errorText);
            }
          }
        });

        // Create a separate reference for the interval ID
        const timeUpdateIntervalRef = { current: null as number | null };

        // Debug variable to track if interval is running
        window.debugYouTubeInterval = {
          isRunning: false,
          lastUpdateTime: Date.now(),
          updates: 0,
          lastTime: 0
        };

        // Variables for seek detection
        let lastKnownTime = 0;
        let lastUpdateTimestamp = Date.now();
        let isSeekDetectionEnabled = true;
        let lastShiftClickTime = 0; // Track when shift+clicks happen to avoid false seek detection

        // Function to start the time update interval
        const startTimeUpdateInterval = () => {
          // Clear any existing interval first
          if (timeUpdateIntervalRef.current !== null) {
            clearInterval(timeUpdateIntervalRef.current);
          }
          window.debugYouTubeInterval.isRunning = true;
          window.debugYouTubeInterval.lastUpdateTime = Date.now();
          window.debugYouTubeInterval.updates = 0;

          // Reset seek detection variables
          try {
            lastKnownTime = playerRef.current?.getCurrentTime() || 0;
          } catch (error) {
            lastKnownTime = 0;
          }
          lastUpdateTimestamp = Date.now();
          isSeekDetectionEnabled = true;

          // Start a new interval
          timeUpdateIntervalRef.current = window.setInterval(() => {
            if (playerRef.current && playerRef.current.getCurrentTime) {
              try {
                const currentTime = playerRef.current.getCurrentTime();
                const now = Date.now();
                const timeDelta = (now - lastUpdateTimestamp) / 1000; // Convert to seconds
                const expectedTime = lastKnownTime + timeDelta;

                // Detect user-initiated seeks by checking for unexpected time jumps
                const timeDifference = Math.abs(currentTime - expectedTime);
                const seekThreshold = 0.5; // 0.5 second threshold for detecting seeks

                // Only detect seeks if we have a reasonable time delta and significant difference
                // Also check if the player is paused, as user seeks often happen during pause
                const playerState = playerRef.current.getPlayerState?.();
                const isPaused = playerState === window.YT.PlayerState.PAUSED;

                // Check if a shift+click happened recently (within 1 second) to avoid false detection
                const timeSinceShiftClick = now - lastShiftClickTime;
                const isRecentShiftClick = timeSinceShiftClick < 1000;

                if (isSeekDetectionEnabled && timeDifference > seekThreshold && timeDelta < 2.0 && timeDelta > 0.01 && !isRecentShiftClick) {
                  // This looks like a user-initiated seek

                  // Set flag to prevent the external currentTime useEffect from interfering
                  isHandlingSeekEventRef.current = true;

                  // Update the store immediately
                  setCurrentTime(currentTime);

                  // Reset the flag after a longer delay if paused (to allow for user interaction)
                  const flagResetDelay = isPaused ? 300 : 100;
                  setTimeout(() => {
                    isHandlingSeekEventRef.current = false;
                  }, flagResetDelay);

                  // Dispatch an event to notify the timeline
                  window.dispatchEvent(new CustomEvent('youtube-seek-confirmed', {
                    detail: { time: currentTime }
                  }));

                  // Temporarily disable seek detection to avoid false positives
                  // Use a longer delay if paused to allow for multiple user interactions
                  const detectionResetDelay = isPaused ? 800 : 500;
                  isSeekDetectionEnabled = false;
                  setTimeout(() => {
                    isSeekDetectionEnabled = true;
                  }, detectionResetDelay);
                }

                // Update tracking variables
                lastKnownTime = currentTime;
                lastUpdateTimestamp = now;

                // Clamp to loop start if before region
                if (loopActiveRef.current && currentTime < loopStartRef.current) {
                  playerRef.current.seekTo(loopStartRef.current, true);
                  setCurrentTime(loopStartRef.current);
                  return;
                }
                window.debugYouTubeInterval.updates++;
                window.debugYouTubeInterval.lastUpdateTime = Date.now();
                window.debugYouTubeInterval.lastTime = currentTime;

                // Update the store with the current time
                setCurrentTime(currentTime);

                // Dispatch a time update event for other components
                window.dispatchEvent(new CustomEvent('youtube-time-update', {
                  detail: { time: currentTime }
                }));

                // Handle looping (use refs so values are always fresh)
                if (loopActiveRef.current && !window.isHoldingEndForLoop && currentTime >= (loopEndRef.current - 0.2)) {

                  // Optional delay before restarting the loop
                  if (loopDelayRef.current > 0) {
                    playerRef.current.pauseVideo();
                    setTimeout(() => {
                      if (playerRef.current) {
                        playerRef.current.seekTo(loopStartRef.current, true);
                        playerRef.current.playVideo();
                        setCurrentTime(loopStartRef.current);
                        window.dispatchEvent(new CustomEvent('youtube-loop', {
                          detail: { time: loopStartRef.current }
                        }));
                      }
                    }, loopDelayRef.current * 1000);
                  } else {
                    playerRef.current.seekTo(loopStartRef.current, true);
                    setCurrentTime(loopStartRef.current);
                    window.dispatchEvent(new CustomEvent('youtube-loop', {
                      detail: { time: loopStartRef.current }
                    }));
                  }
                }
              } catch (error) {
                window.debugYouTubeInterval.isRunning = false;
              }
            }
          }, 33); // ~30fps for very smooth updates
        };

        // Start the interval immediately
        startTimeUpdateInterval();

        // Listen for seek events to restart the interval
        const handleSeekConfirmed = (e: Event) => {
          startTimeUpdateInterval();
        };

        // Add a global debug function to restart the interval
        window.restartYouTubeInterval = startTimeUpdateInterval;

        window.addEventListener('youtube-seek-confirmed', handleSeekConfirmed);
        window.addEventListener('youtube-loop', handleSeekConfirmed);
        window.addEventListener('youtube-seek', handleSeekConfirmed);

        // Listen for shift+click events to avoid false seek detection
        const handleShiftClick = () => {
          lastShiftClickTime = Date.now();
        };
        window.addEventListener('youtube-shift-click', handleShiftClick);

        return () => {
          if (timeUpdateIntervalRef.current !== null) {
            clearInterval(timeUpdateIntervalRef.current);
          }
          window.removeEventListener('youtube-seek-confirmed', handleSeekConfirmed);
          window.removeEventListener('youtube-loop', handleSeekConfirmed);
          window.removeEventListener('youtube-shift-click', handleShiftClick);
        };
      } catch (error) {
        setErrorMessage('Failed to initialize YouTube player.');
      }
    }
  }, [
    isApiReady,
    videoId,
    setDuration,
    setCurrentTime,
    setIsPlaying,
    volume
  ]);

  // Handle play/pause state changes
  useEffect(() => {
    if (playerRef.current) {
      try {
        // Check if the player state actually needs changing
        // If loop is active, ensure we start within the loop region
        if (isPlaying && loopActiveRef.current) {
          const pos = playerRef.current.getCurrentTime();
          if (pos < loopStartRef.current) {
            playerRef.current.seekTo(loopStartRef.current, true);
            setCurrentTime(loopStartRef.current);
            return;
          }
        }
        const currentState = playerRef.current.getPlayerState();
        const targetStatePlaying = window.YT.PlayerState.PLAYING;

        if (isPlaying && currentState !== targetStatePlaying) {
          // Avoid calling playVideo if already buffering or cued
          if (currentState !== window.YT.PlayerState.BUFFERING && currentState !== window.YT.PlayerState.CUED) {
            playerRef.current.playVideo();
          }
        } else if (!isPlaying && currentState === targetStatePlaying) {
          // Pause if store says !isPlaying and player is currently playing
          playerRef.current.pauseVideo();
        }
      } catch (error) {
        // Error controlling playback
      }
    }
  }, [isPlaying]); // Dependency: isPlaying from the store

  // Handle external currentTime changes (e.g., stop button setting time to 0)
  // Use a ref to track the last time we handled an external seek to avoid rapid-fire seeks
  const lastExternalSeekTimeRef = useRef(0);

  // useEffect(() => {
  //   if (playerRef.current && !isPlaying && !isHandlingSeekEventRef.current) { // Only seek when paused and not handling a seek event
  //     try {
  //       const playerTime = playerRef.current.getCurrentTime();
  //       // Check if store time significantly differs from player time
  //       // This avoids loops with the time update interval
  //       const timeDifference = Math.abs(currentTime - playerTime);
  //       const now = Date.now();

  //       // Only seek if:
  //       // 1. The difference is significant (> 0.5s to avoid interference with user seeks)
  //       // 2. Enough time has passed since the last external seek (> 200ms)
  //       // 3. The currentTime change wasn't caused by our own seek detection
  //       if (timeDifference > 0.05 && (now - lastExternalSeekTimeRef.current) > 200) { // Reduced threshold from 0.5 to 0.05

  //         lastExternalSeekTimeRef.current = now;
  //         playerRef.current.seekTo(currentTime, true);
  //       }
  //     } catch (error) {
  //       // Error handling external currentTime change
  //     }
  //   }
  // // We only want this effect to run when currentTime changes *while paused*
  // // We also need isPlaying in the dependency array to re-evaluate when pausing/playing
  // }, [currentTime, isPlaying]);

  // Handle volume changes
  useEffect(() => {
    if (playerRef.current) {
      try {
        playerRef.current.setVolume(volume * 100);
      } catch (error) {
        // Error setting volume
      }
    }
  }, [volume]);

  useImperativeHandle(ref, () => ({
    getCurrentTime: () => {
      return playerRef.current?.getCurrentTime();
    },
    getPlayerState: () => {
      return playerRef.current?.getPlayerState();
    }
    // Add other exposed methods here if needed
  }));

  // Handle clicks on the YouTube player container
  const handlePlayerClick = useCallback((e: React.MouseEvent) => {
    if (e.shiftKey) {
      // Shift+click detected on the YouTube player

      // Get the current time and dispatch an event that the timeline can handle
      if (playerRef.current) {
        try {
          const currentTime = playerRef.current.getCurrentTime();

          // Dispatch a custom event that the timeline can listen for
          window.dispatchEvent(new CustomEvent('youtube-shift-click', {
            detail: { time: currentTime }
          }));

          // Prevent the default click behavior
          e.preventDefault();
          e.stopPropagation();
        } catch (error) {
          // Error handling shift+click on YouTube player
        }
      }
    }
  }, []);

  return (
    <div
      style={{ width: '100%', aspectRatio: '16/9', backgroundColor: 'black', position: 'relative' }}
      onClick={handlePlayerClick}
    >
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />

      {errorMessage && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          zIndex: 10
        }}>
          <div style={{ marginBottom: '1rem', color: '#ff4444' }}>Error</div>
          <div style={{ fontSize: '0.9rem', textAlign: 'center', maxWidth: '80%' }}>{errorMessage}</div>
        </div>
      )}


    </div>
  );
});


export default React.memo(YouTubePlayer);
