diff --git a/src/components/MidiTimelineVisualization.tsx b/src/components/MidiTimelineVisualization.tsx
index 31a7ec6..98b0045 100644
--- a/src/components/MidiTimelineVisualization.tsx
+++ b/src/components/MidiTimelineVisualization.tsx
@@ -28,7 +28,13 @@ const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
   const [verticalZoom, setVerticalZoom] = useState(1);
   const [horizontalZoom, setHorizontalZoom] = useState(1);
   const [isExpanded, setIsExpanded] = useState(false);
+  const [noteHeight, setNoteHeight] = useState(12); // Default note height in pixels
+  const [stickyPlayhead, setStickyPlayhead] = useState(true); // Playhead follows playback
+  const [ultraStickyMode, setUltraStickyMode] = useState(false); // Ultra-sticky mode with frame-by-frame following
   const containerRef = useRef<HTMLDivElement>(null);
+  const scrollContainerRef = useRef<HTMLDivElement>(null);
+  const animationFrameRef = useRef<number | null>(null);
+  const lastScrollTargetRef = useRef<number>(0);
   
   // Calculate effective height based on expansion and zoom
   const effectiveHeight = isExpanded ? Math.max(height * 2, 200) : height;
@@ -235,6 +241,92 @@ const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
   const playheadPixels = timeToPixels(currentTime);
   const totalWidthPixels = totalBeats * pixelsPerBeat * zoomLevel * horizontalZoom;
 
+  // Ultra-sticky playhead with frame-by-frame following
+  React.useEffect(() => {
+    if (!ultraStickyMode || !scrollContainerRef.current || referenceStartTime === null) {
+      // Cancel any existing animation frame
+      if (animationFrameRef.current) {
+        cancelAnimationFrame(animationFrameRef.current);
+        animationFrameRef.current = null;
+      }
+      return;
+    }
+
+    const scrollContainer = scrollContainerRef.current;
+    const containerWidth = scrollContainer.clientWidth;
+
+    const updateScroll = () => {
+      if (!scrollContainer || !ultraStickyMode || referenceStartTime === null) return;
+
+      const playheadPosition = playheadPixels;
+      const centerPosition = containerWidth / 2;
+      const targetScrollLeft = Math.max(0, playheadPosition - centerPosition);
+
+      // Smooth interpolation for ultra-smooth following
+      const currentScrollLeft = scrollContainer.scrollLeft;
+      const scrollDiff = targetScrollLeft - currentScrollLeft;
+      const smoothingFactor = 0.15; // Adjust for smoothness (0.1 = very smooth, 0.3 = more responsive)
+
+      if (Math.abs(scrollDiff) > 0.5) { // Only update if there's meaningful difference
+        const newScrollLeft = currentScrollLeft + (scrollDiff * smoothingFactor);
+        scrollContainer.scrollLeft = newScrollLeft;
+        lastScrollTargetRef.current = newScrollLeft;
+      }
+
+      // Continue the animation loop
+      animationFrameRef.current = requestAnimationFrame(updateScroll);
+    };
+
+    // Start the animation loop
+    animationFrameRef.current = requestAnimationFrame(updateScroll);
+
+    return () => {
+      if (animationFrameRef.current) {
+        cancelAnimationFrame(animationFrameRef.current);
+        animationFrameRef.current = null;
+      }
+    };
+  }, [playheadPixels, ultraStickyMode, referenceStartTime]);
+
+  // Regular sticky playhead (fallback when ultra-sticky is disabled)
+  React.useEffect(() => {
+    if (ultraStickyMode || !stickyPlayhead || !scrollContainerRef.current || referenceStartTime === null) return;
+
+    const scrollContainer = scrollContainerRef.current;
+    const containerWidth = scrollContainer.clientWidth;
+    const scrollLeft = scrollContainer.scrollLeft;
+
+    // Calculate if playhead is outside the visible area
+    const playheadPosition = playheadPixels;
+    const leftEdge = scrollLeft;
+    const rightEdge = scrollLeft + containerWidth;
+
+    // Keep playhead in the center third of the visible area
+    const centerThird = containerWidth / 3;
+    const targetScrollLeft = playheadPosition - centerThird;
+
+    // Only scroll if playhead is getting close to edges or outside visible area
+    if (playheadPosition < leftEdge + centerThird || playheadPosition > rightEdge - centerThird) {
+      scrollContainer.scrollTo({
+        left: Math.max(0, targetScrollLeft),
+        behavior: 'smooth'
+      });
+    }
+  }, [playheadPixels, stickyPlayhead, referenceStartTime, ultraStickyMode]);
+
+  // Keyboard shortcut for ultra-sticky mode (U key)
+  React.useEffect(() => {
+    const handleKeyDown = (e: KeyboardEvent) => {
+      if (e.key.toLowerCase() === 'u' && !e.ctrlKey && !e.metaKey && !e.altKey) {
+        e.preventDefault();
+        setUltraStickyMode(prev => !prev);
+      }
+    };
+
+    window.addEventListener('keydown', handleKeyDown);
+    return () => window.removeEventListener('keydown', handleKeyDown);
+  }, []);
+
   return (
     <div 
       ref={containerRef}
@@ -330,6 +422,72 @@ const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
           >
             V+
           </button>
+          <button
+            onClick={() => setNoteHeight(prev => Math.max(6, prev - 2))}
+            style={{
+              padding: '2px 6px',
+              fontSize: '10px',
+              backgroundColor: '#f3f4f6',
+              border: '1px solid #d1d5db',
+              borderRadius: '2px',
+              cursor: 'pointer'
+            }}
+            disabled={noteHeight <= 6}
+            title="Decrease note height"
+          >
+            N-
+          </button>
+          <span style={{ fontSize: '9px', color: '#6b7280', minWidth: '25px', textAlign: 'center' }}>
+            {noteHeight}px
+          </span>
+          <button
+            onClick={() => setNoteHeight(prev => Math.min(24, prev + 2))}
+            style={{
+              padding: '2px 6px',
+              fontSize: '10px',
+              backgroundColor: '#f3f4f6',
+              border: '1px solid #d1d5db',
+              borderRadius: '2px',
+              cursor: 'pointer'
+            }}
+            disabled={noteHeight >= 24}
+            title="Increase note height"
+          >
+            N+
+          </button>
+          <button
+            onClick={() => setStickyPlayhead(!stickyPlayhead)}
+            style={{
+              padding: '2px 8px',
+              fontSize: '10px',
+              backgroundColor: stickyPlayhead ? '#22c55e' : '#f3f4f6',
+              color: stickyPlayhead ? 'white' : '#374151',
+              border: '1px solid #d1d5db',
+              borderRadius: '2px',
+              cursor: 'pointer',
+              marginLeft: '4px'
+            }}
+            title="Toggle sticky playhead (follows playback)"
+          >
+            {stickyPlayhead ? 'Sticky ON' : 'Sticky OFF'}
+          </button>
+          <button
+            onClick={() => setUltraStickyMode(!ultraStickyMode)}
+            style={{
+              padding: '2px 8px',
+              fontSize: '10px',
+              backgroundColor: ultraStickyMode ? '#dc2626' : '#f3f4f6',
+              color: ultraStickyMode ? 'white' : '#374151',
+              border: '1px solid #d1d5db',
+              borderRadius: '2px',
+              cursor: 'pointer',
+              marginLeft: '4px',
+              fontWeight: ultraStickyMode ? 'bold' : 'normal'
+            }}
+            title="Toggle ultra-sticky mode (60fps frame-by-frame following) - Press U key"
+          >
+            {ultraStickyMode ? 'ULTRA' : 'Ultra'}
+          </button>
           <button
             onClick={() => setIsExpanded(!isExpanded)}
             style={{
@@ -362,11 +520,14 @@ const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
         </div>
 
         {/* Timeline Area */}
-        <div style={{
-          flex: 1,
-          position: 'relative',
-          overflow: 'hidden'
-        }}>
+        <div
+          ref={scrollContainerRef}
+          style={{
+            flex: 1,
+            position: 'relative',
+            overflow: 'auto'
+          }}
+        >
           {/* Background grid lines for pitch reference */}
           <div style={{ position: 'absolute', width: '100%', height: '100%' }}>
             {[84, 72, 60, 48, 36].map(pitch => (
@@ -411,9 +572,9 @@ const MidiTimelineVisualization: React.FC<MidiTimelineVisualizationProps> = ({
                   style={{
                     position: 'absolute',
                     left: `${startPixels}px`,
-                    top: `${pitchToY(note.pitch) - 6}px`,
+                    top: `${pitchToY(note.pitch) - noteHeight/2}px`,
                     width: `${Math.max(widthPixels, 8)}px`, // Minimum width for visibility
-                    height: '12px',
+                    height: `${noteHeight}px`,
                     backgroundColor: getNoteColor(note.pitch),
                     borderRadius: '4px',
                     opacity: 0.9,
diff --git a/src/components/midi-editor/BandLabMidiEditor.tsx b/src/components/midi-editor/BandLabMidiEditor.tsx
index 8187146..a354969 100644
--- a/src/components/midi-editor/BandLabMidiEditor.tsx
+++ b/src/components/midi-editor/BandLabMidiEditor.tsx
@@ -47,7 +47,7 @@ export interface MidiEditorState {
   velocity: number;
   notePreview: boolean;
   quantizeValue: string;
-  bpm: number;
+  bpm: number | string; // Allow string for temporary empty state during editing
   referenceStartTime: number | null;
   zoom: number;
   octaveRange: { start: number; end: number };
@@ -60,6 +60,9 @@ export interface MidiEditorState {
   recordingStartBeat: number | null; // Beat position where recording started
   lastOverwriteBeat: number | null; // Last beat position that was overwritten
   currentRecordingSessionId: string | null; // Unique ID for the current recording session
+  noteHeight: number; // Height of notes in pixels
+  stickyPlayhead: boolean; // Whether playhead follows playback
+  ultraStickyMode: boolean; // Ultra-sticky mode with frame-by-frame following
 }
 
 const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
@@ -92,7 +95,10 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     isRecording: false,
     recordingStartBeat: null,
     lastOverwriteBeat: null,
-    currentRecordingSessionId: null
+    currentRecordingSessionId: null,
+    noteHeight: 20, // Default note height in pixels
+    stickyPlayhead: true, // Enable sticky playhead by default
+    ultraStickyMode: false // Ultra-sticky mode disabled by default
   });
 
   // MIDI notes state - initialize with default samples
@@ -131,6 +137,11 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
   // Flag to prevent note playback immediately after a loop
   const justLoopedRef = useRef<boolean>(false);
 
+  // Helper function to get numeric BPM value
+  const getNumericBpm = useCallback(() => {
+    return typeof editorState.bpm === 'number' ? editorState.bpm : 120;
+  }, [editorState.bpm]);
+
   // Utility function to quantize time values (consistent with grid)
   const quantizeTime = useCallback((time: number) => {
     // Handle special case for "free" mode
@@ -164,10 +175,17 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
   const onNotesChangeRef = useRef(onNotesChange);
   onNotesChangeRef.current = onNotesChange;
 
+  // Track previous notes to prevent infinite loops
+  const previousNotesRef = useRef<MidiNote[]>([]);
+
   useEffect(() => {
     // Only notify parent if this is an internal change (user action)
     // Skip notification if the change came from the parent (external update)
-    if (onNotesChangeRef.current && !isInternalUpdateRef.current) {
+    // Also skip if notes haven't actually changed (prevent infinite loops)
+    const notesChanged = JSON.stringify(midiNotes) !== JSON.stringify(previousNotesRef.current);
+
+    if (onNotesChangeRef.current && !isInternalUpdateRef.current && notesChanged) {
+      previousNotesRef.current = [...midiNotes]; // Store a copy
       onNotesChangeRef.current(midiNotes);
     }
     // Reset the flag after processing
@@ -181,14 +199,14 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
   useEffect(() => {
     if (onEditorStateChangeRef.current) {
       onEditorStateChangeRef.current({
-        bpm: editorState.bpm,
+        bpm: getNumericBpm(),
         referenceStartTime: editorState.referenceStartTime,
         selectedKey: editorState.selectedKey,
         selectedMode: editorState.selectedMode,
         zoom: editorState.zoom
       });
     }
-  }, [editorState.bpm, editorState.referenceStartTime, editorState.selectedKey, editorState.selectedMode, editorState.zoom]);
+  }, [editorState.bpm, editorState.referenceStartTime, editorState.selectedKey, editorState.selectedMode, editorState.zoom, getNumericBpm]);
 
   // Update playhead position using the same time source as singing visualization
   // Use ref to prevent infinite re-renders and memoize the update
@@ -256,7 +274,13 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
         playingNotesRef.current.clear();
       }
 
-
+      // IMPORTANT: Even when MIDI playback is disabled, we still need to prevent interference
+      // with manual notes. Check if any manual notes should be protected from other systems.
+      if (isPlaying && playingScaleNotesRef.current.size > 0) {
+        // Video is playing but no reference time set - ensure manual notes are protected
+        // by preventing any automatic note stopping for manually played pitches
+        console.log('🛡️ Protecting manual notes during playback without reference time');
+      }
 
       lastPlayheadTimeRef.current = currentPlayheadTime;
       return;
@@ -270,7 +294,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     const elapsedVideoTime = currentVideoTime - editorState.referenceStartTime;
     if (elapsedVideoTime < 0) return; // Before reference start time
 
-    const beatsPerSecond = editorState.bpm / 60;
+    const beatsPerSecond = getNumericBpm() / 60;
     const currentBeat = elapsedVideoTime * beatsPerSecond;
 
     // Calculate the time range we need to check for note triggers
@@ -284,14 +308,34 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       const noteEndBeat = note.startTime + note.duration;
       const isCurrentlyPlaying = playingNotesRef.current.has(note.id);
 
+      // Skip recording notes - they are handled separately by the recording system
+      const isRecordingNote = typeof note.id === 'string' && note.id.startsWith('rec_');
+      if (isRecordingNote && editorState.isRecording) {
+        // console.log('🎵 Skipping recording note from playback:', note.id);
+        return; // Don't interfere with recording notes
+      }
+
+      // Check if this pitch is currently being played manually (keyboard input)
+      const isManuallyPlaying = Array.from(playingScaleNotesRef.current.values()).includes(note.pitch);
+
       // Check if note should start playing
       if (!isCurrentlyPlaying &&
+          !isManuallyPlaying && // Don't auto-play if manually playing the same pitch
           noteStartBeat >= lastBeat &&
           noteStartBeat <= currentBeat) {
 
+        // DOUBLE CHECK: Make sure we're not interfering with manual notes
+        // Check again right before playing to prevent race conditions
+        const isStillManuallyPlaying = Array.from(playingScaleNotesRef.current.values()).includes(note.pitch);
+        if (isStillManuallyPlaying) {
+          console.log(`🛡️ Preventing auto-play of note ${note.pitch} - manually playing`);
+          return; // Skip this note completely
+        }
+
         // Calculate how long the note should play
         const remainingDuration = Math.max(0.1, noteEndBeat - currentBeat) / beatsPerSecond;
 
+        console.log(`🎵 Auto-playing note ${note.pitch} for ${remainingDuration.toFixed(3)}s`);
         audioSynth.playNote(
           note.pitch,
           remainingDuration,
@@ -303,13 +347,13 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       }
 
       // Check if note should stop playing (if it has ended)
-      if (isCurrentlyPlaying && currentBeat > noteEndBeat) {
+      if (isCurrentlyPlaying && currentBeat > noteEndBeat && !isManuallyPlaying) {
         audioSynth.stopNote(note.pitch);
         playingNotesRef.current.delete(note.id);
       }
     });
 
-  }, [currentPlayheadTime, editorState.referenceStartTime, editorState.bpm, isPlaying]);
+  }, [currentPlayheadTime, editorState.referenceStartTime, editorState.bpm, isPlaying, getNumericBpm]);
 
   // Set reference time handler using audio store time for consistency
   // Memoize to prevent unnecessary re-renders
@@ -431,7 +475,10 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     );
   }, [selectedNotes, getMovementIncrement, quantizeDuration]);
 
-  // Play scale degree note (start playing)
+  // Track note start times for non-recording mode
+  const nonRecordingStartTimesRef = useRef<Map<number, number>>(new Map());
+
+  // Play scale degree note (start playing) - non-recording mode
   const playScaleDegree = useCallback((degree: number) => {
     try {
       // Don't play if already playing this degree
@@ -446,7 +493,10 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
         editorState.selectedOctave
       );
 
-      // Play note indefinitely (undefined duration means play until manually stopped)
+      // Record start time for duration calculation
+      nonRecordingStartTimesRef.current.set(degree, Date.now());
+
+      // Play note indefinitely for immediate audio feedback (same as recording mode)
       audioSynth.playNote(midiNote, undefined, editorState.velocity || 80);
 
       // Track that this scale degree is playing
@@ -456,12 +506,32 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     }
   }, [editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.velocity]);
 
-  // Stop scale degree note
+  // Stop scale degree note - non-recording mode
   const stopScaleDegree = useCallback((degree: number) => {
     const midiNote = playingScaleNotesRef.current.get(degree);
+
     if (midiNote !== undefined) {
+      // Calculate the actual duration the key was held (for logging/debugging)
+      const startTime = nonRecordingStartTimesRef.current.get(degree);
+      if (startTime) {
+        const durationMs = Date.now() - startTime;
+        const durationSeconds = durationMs / 1000;
+
+        console.log(`🎵 Note ${degree} held for ${durationSeconds.toFixed(3)}s`);
+
+        nonRecordingStartTimesRef.current.delete(degree);
+      }
+
+      // Stop the note properly
+      console.log(`🛑 Stopping note ${midiNote} for degree ${degree}`);
+
+      // Stop via audioSynth
       audioSynth.stopNote(midiNote);
+
+      // Remove from tracking immediately
       playingScaleNotesRef.current.delete(degree);
+
+      console.log(`🛑 Note ${degree} stopped`);
     }
   }, []);
 
@@ -473,7 +543,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
 
     const currentTime = getCurrentVideoTime?.() || 0;
     const currentBeat = editorState.referenceStartTime !== null
-      ? (currentTime - editorState.referenceStartTime) * (editorState.bpm / 60)
+      ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
       : 0;
 
     // Update all currently recording notes
@@ -511,7 +581,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       // Force update even if no duration changes to ensure visual feedback
       return hasUpdates ? updatedNotes : prev;
     });
-  }, [editorState.isRecording, editorState.referenceStartTime, editorState.bpm, editorState.quantizeValue, getCurrentVideoTime, quantizeTime]);
+  }, [editorState.isRecording, editorState.referenceStartTime, editorState.bpm, editorState.quantizeValue, getCurrentVideoTime, quantizeTime, getNumericBpm]);
 
   // Stop real-time updates when recording stops
   useEffect(() => {
@@ -544,10 +614,15 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
         editorState.selectedOctave
       );
 
+      // Don't start recording if this note is already being recorded or played
+      if (recordingNotesRef.current.has(midiNote) || playingScaleNotesRef.current.has(degree)) {
+        return;
+      }
+
       // Get current time for recording
       const currentTime = getCurrentVideoTime?.() || 0;
       const currentBeat = editorState.referenceStartTime !== null
-        ? (currentTime - editorState.referenceStartTime) * (editorState.bpm / 60)
+        ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
         : 0;
 
       // Quantize the start time based on current quantize setting
@@ -589,12 +664,15 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
         recordingUpdateIntervalRef.current = setInterval(updateRecordingNoteDurations, 30);
       }
 
-      // Play the note for audio feedback
+      // Play the note for audio feedback AND track it for proper stopping
       audioSynth.playNote(midiNote, undefined, editorState.velocity || 80);
+
+      // Track the note in the scale degree mapping for consistent stopping
+      playingScaleNotesRef.current.set(degree, midiNote);
     } catch (error) {
       console.error('Error starting recording note:', error);
     }
-  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.velocity, getCurrentVideoTime, quantizeTime]);
+  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.velocity, getCurrentVideoTime, quantizeTime, getNumericBpm]);
 
   const stopRecordingNote = useCallback((degree: number) => {
     if (!editorState.isRecording) return;
@@ -615,7 +693,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       // Get current time for recording
       const currentTime = getCurrentVideoTime?.() || 0;
       const currentBeat = editorState.referenceStartTime !== null
-        ? (currentTime - editorState.referenceStartTime) * (editorState.bpm / 60)
+        ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
         : recordingInfo.startTime + 0.25; // Fallback duration
 
       // Calculate duration
@@ -646,8 +724,9 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       // Stop audio playback
       audioSynth.stopNote(midiNote);
 
-      // Remove from recording tracking
+      // Remove from both recording tracking and scale degree tracking
       recordingNotesRef.current.delete(midiNote);
+      playingScaleNotesRef.current.delete(degree);
 
       // Stop real-time updates if no more notes are being recorded
       if (recordingNotesRef.current.size === 0 && recordingUpdateIntervalRef.current) {
@@ -657,7 +736,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     } catch (error) {
       console.error('Error stopping recording note:', error);
     }
-  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.quantizeValue, editorState.freeMovement, getCurrentVideoTime, quantizeTime]);
+  }, [editorState.isRecording, editorState.selectedKey, editorState.selectedMode, editorState.selectedOctave, editorState.referenceStartTime, editorState.bpm, editorState.quantizeValue, editorState.freeMovement, getCurrentVideoTime, quantizeTime, getNumericBpm]);
 
   // Map keys to scale degrees (moved outside useEffect to avoid duplication)
   const getScaleDegreeFromKey = useCallback((key: string, code: string): number | null => {
@@ -812,6 +891,10 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
             moveSelectedNotes(getMovementIncrement(), 0); // Move right in time
           }
           break;
+        case 'u':
+          event.preventDefault();
+          setEditorState(prev => ({ ...prev, ultraStickyMode: !prev.ultraStickyMode }));
+          break;
 
       }
     };
@@ -859,6 +942,12 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       playingScaleNotesRef.current.clear();
       pressedScaleKeysRef.current.clear();
 
+      // Clear non-recording start times
+      nonRecordingStartTimesRef.current.clear();
+
+      // Clear non-recording start times
+      nonRecordingStartTimesRef.current.clear();
+
       // Stop all recording notes
       recordingNotesRef.current.forEach(({ noteId }, midiNote) => {
         audioSynth.stopNote(midiNote);
@@ -903,6 +992,9 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       });
       recordingNotesRef.current.clear();
 
+      // Also clear scale degree tracking for recording notes
+      playingScaleNotesRef.current.clear();
+
       // Stop real-time updates
       if (recordingUpdateIntervalRef.current) {
         clearInterval(recordingUpdateIntervalRef.current);
@@ -932,7 +1024,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       if (editorState.recordingStartBeat === null) {
         const currentTime = getCurrentVideoTime?.() || 0;
         const currentBeat = editorState.referenceStartTime !== null
-          ? (currentTime - editorState.referenceStartTime) * (editorState.bpm / 60)
+          ? (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60)
           : 0;
 
         // Generate a unique session ID for this recording session
@@ -946,7 +1038,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
         }));
       }
     }
-  }, [editorState.isRecording, editorState.referenceStartTime, editorState.bpm, getCurrentVideoTime]);
+  }, [editorState.isRecording, editorState.referenceStartTime, editorState.bpm, getCurrentVideoTime, getNumericBpm]);
 
   // Continuous overwrite during recording - remove existing notes as playback progresses
   useEffect(() => {
@@ -955,7 +1047,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
     }
 
     const currentTime = getCurrentVideoTime?.() || 0;
-    const currentBeat = (currentTime - editorState.referenceStartTime) * (editorState.bpm / 60);
+    const currentBeat = (currentTime - editorState.referenceStartTime) * (getNumericBpm() / 60);
 
     // Detect if playback has moved backward (loop restart, seek, etc.)
     const hasMovedBackward = editorState.lastOverwriteBeat !== null && currentBeat < editorState.lastOverwriteBeat - 0.1;
@@ -1020,7 +1112,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
       lastOverwriteBeat: currentBeat
     }));
 
-  }, [currentTime, isPlaying, editorState.isRecording, editorState.referenceStartTime, editorState.bpm, editorState.lastOverwriteBeat, getCurrentVideoTime]);
+  }, [currentTime, isPlaying, editorState.isRecording, editorState.referenceStartTime, editorState.bpm, editorState.lastOverwriteBeat, getCurrentVideoTime, getNumericBpm]);
 
   // Update editor state handlers
   const updateEditorState = (updates: Partial<MidiEditorState>) => {
@@ -1305,7 +1397,7 @@ const BandLabMidiEditor: React.FC<BandLabMidiEditorProps> = ({
 
       {/* Metronome Component */}
       <Metronome
-        bpm={editorState.bpm}
+        bpm={getNumericBpm()}
         isPlaying={isPlaying}
         metronomeEnabled={editorState.metronomeEnabled}
         getCurrentVideoTime={getCurrentVideoTime}
diff --git a/src/components/midi-editor/BandLabMidiHeader.tsx b/src/components/midi-editor/BandLabMidiHeader.tsx
index 321e509..a284cd3 100644
--- a/src/components/midi-editor/BandLabMidiHeader.tsx
+++ b/src/components/midi-editor/BandLabMidiHeader.tsx
@@ -49,8 +49,28 @@ const BandLabMidiHeader: React.FC<BandLabMidiHeaderProps> = ({
   };
 
   const handleBpmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
-    const bpm = parseInt(e.target.value) || 120;
-    updateEditorState({ bpm });
+    const value = e.target.value;
+
+    // Allow empty string temporarily for user to clear the field
+    if (value === '') {
+      updateEditorState({ bpm: '' as any }); // Temporarily allow empty string
+      return;
+    }
+
+    const bpm = parseInt(value, 10);
+    // Only update if it's a valid positive number
+    if (!isNaN(bpm) && bpm > 0) {
+      updateEditorState({ bpm });
+    }
+  };
+
+  const handleBpmBlur = (e: React.FocusEvent<HTMLInputElement>) => {
+    const value = e.target.value;
+
+    // If field is empty or invalid when user leaves it, restore default BPM
+    if (value === '' || isNaN(parseInt(value, 10)) || parseInt(value, 10) <= 0) {
+      updateEditorState({ bpm: 120 });
+    }
   };
 
   const handleQuantizeChange = (quantizeValue: string) => {
@@ -198,10 +218,12 @@ const BandLabMidiHeader: React.FC<BandLabMidiHeaderProps> = ({
           <input
             type="number"
             className="midi-editor-input"
-            value={editorState.bpm}
+            value={editorState.bpm === '' ? '' : editorState.bpm}
             onChange={handleBpmChange}
+            onBlur={handleBpmBlur}
             min="1"
             max="300"
+            placeholder="120"
           />
           <button
             className="midi-editor-button compact"
@@ -238,6 +260,52 @@ const BandLabMidiHeader: React.FC<BandLabMidiHeaderProps> = ({
           </button>
         </div>
 
+        <div className="midi-editor-header-section">
+          <span>Note Height:</span>
+          <button
+            className="midi-editor-button compact"
+            onClick={() => updateEditorState({ noteHeight: Math.max(10, (editorState.noteHeight || 20) - 2) })}
+            disabled={(editorState.noteHeight || 20) <= 10}
+            title="Decrease note height"
+          >
+            -
+          </button>
+          <span className="tabular-nums" style={{ minWidth: '30px', textAlign: 'center' }}>
+            {editorState.noteHeight || 20}px
+          </span>
+          <button
+            className="midi-editor-button compact"
+            onClick={() => updateEditorState({ noteHeight: Math.min(40, (editorState.noteHeight || 20) + 2) })}
+            disabled={(editorState.noteHeight || 20) >= 40}
+            title="Increase note height"
+          >
+            +
+          </button>
+        </div>
+
+        <div className="midi-editor-header-section">
+          <button
+            className={`midi-editor-button compact ${editorState.stickyPlayhead ? 'active' : ''}`}
+            onClick={() => updateEditorState({ stickyPlayhead: !editorState.stickyPlayhead })}
+            title="Toggle sticky playhead (follows playback)"
+          >
+            {editorState.stickyPlayhead ? 'Sticky ON' : 'Sticky OFF'}
+          </button>
+          <button
+            className={`midi-editor-button compact ${editorState.ultraStickyMode ? 'active ultra-sticky' : ''}`}
+            onClick={() => updateEditorState({ ultraStickyMode: !editorState.ultraStickyMode })}
+            title="Toggle ultra-sticky mode (60fps frame-by-frame following) - Press U key"
+            style={{
+              backgroundColor: editorState.ultraStickyMode ? '#dc2626' : undefined,
+              color: editorState.ultraStickyMode ? 'white' : undefined,
+              fontWeight: editorState.ultraStickyMode ? 'bold' : 'normal',
+              marginLeft: '4px'
+            }}
+          >
+            {editorState.ultraStickyMode ? 'ULTRA' : 'Ultra'}
+          </button>
+        </div>
+
 
       </div>
 
diff --git a/src/components/midi-editor/BandLabNoteGrid.tsx b/src/components/midi-editor/BandLabNoteGrid.tsx
index 47b5021..00c4350 100644
--- a/src/components/midi-editor/BandLabNoteGrid.tsx
+++ b/src/components/midi-editor/BandLabNoteGrid.tsx
@@ -128,6 +128,8 @@ const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
   const rulerRef = useRef<HTMLDivElement>(null);
   const scrollContainerRef = useRef<HTMLDivElement>(null);
   const svgRef = useRef<SVGSVGElement>(null);
+  const animationFrameRef = useRef<number | null>(null);
+  const lastScrollTargetRef = useRef<number>(0);
   const [gridDimensions, setGridDimensions] = useState({ width: 0, height: 0 });
   const [selectedNotes, setSelectedNotes] = useState<Set<string | number>>(new Set());
   const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
@@ -144,7 +146,7 @@ const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
 
   // Grid configuration
   const pixelsPerBeat = 40 * editorState.zoom;
-  const pixelsPerSemitone = 20;
+  const pixelsPerSemitone = editorState.noteHeight || 20; // Use configurable note height
   const beatsPerMeasure = 4;
   const totalSemitones = (editorState.octaveRange.end - editorState.octaveRange.start + 1) * 12;
 
@@ -323,7 +325,8 @@ const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
     const measureStartBeat = measureNumber * beatsPerMeasure;
 
     // Convert beats to seconds using BPM
-    const beatsPerSecond = editorState.bpm / 60;
+    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
+    const beatsPerSecond = bpm / 60;
     const measureStartTimeInSeconds = measureStartBeat / beatsPerSecond;
 
     // Calculate the video time by adding to the reference start time
@@ -352,7 +355,8 @@ const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
     const beat = clickX / pixelsPerBeat;
 
     // Convert beats to seconds using BPM
-    const beatsPerSecond = editorState.bpm / 60;
+    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
+    const beatsPerSecond = bpm / 60;
     const timeInSeconds = beat / beatsPerSecond;
 
     // Calculate the video time by adding to the reference start time
@@ -675,13 +679,87 @@ const BandLabNoteGrid: React.FC<BandLabNoteGridProps> = ({
     const elapsedTime = currentPlayheadTime - editorState.referenceStartTime;
     if (elapsedTime < 0) return null;
 
-    const beatsPerSecond = editorState.bpm / 60;
+    const bpm = typeof editorState.bpm === 'number' ? editorState.bpm : 120;
+    const beatsPerSecond = bpm / 60;
     const elapsedBeats = elapsedTime * beatsPerSecond;
     return elapsedBeats * pixelsPerBeat;
   };
 
   const playheadX = getPlayheadPosition();
 
+  // Ultra-sticky playhead with frame-by-frame following
+  useEffect(() => {
+    if (!editorState.ultraStickyMode || !scrollContainerRef.current || playheadX === null) {
+      // Cancel any existing animation frame
+      if (animationFrameRef.current) {
+        cancelAnimationFrame(animationFrameRef.current);
+        animationFrameRef.current = null;
+      }
+      return;
+    }
+
+    const scrollContainer = scrollContainerRef.current;
+    const containerWidth = scrollContainer.clientWidth;
+
+    const updateScroll = () => {
+      if (!scrollContainer || !editorState.ultraStickyMode || playheadX === null) return;
+
+      const playheadPosition = playheadX;
+      const centerPosition = containerWidth / 2;
+      const targetScrollLeft = Math.max(0, playheadPosition - centerPosition);
+
+      // Smooth interpolation for ultra-smooth following
+      const currentScrollLeft = scrollContainer.scrollLeft;
+      const scrollDiff = targetScrollLeft - currentScrollLeft;
+      const smoothingFactor = 0.15; // Adjust for smoothness (0.1 = very smooth, 0.3 = more responsive)
+
+      if (Math.abs(scrollDiff) > 0.5) { // Only update if there's meaningful difference
+        const newScrollLeft = currentScrollLeft + (scrollDiff * smoothingFactor);
+        scrollContainer.scrollLeft = newScrollLeft;
+        lastScrollTargetRef.current = newScrollLeft;
+      }
+
+      // Continue the animation loop
+      animationFrameRef.current = requestAnimationFrame(updateScroll);
+    };
+
+    // Start the animation loop
+    animationFrameRef.current = requestAnimationFrame(updateScroll);
+
+    return () => {
+      if (animationFrameRef.current) {
+        cancelAnimationFrame(animationFrameRef.current);
+        animationFrameRef.current = null;
+      }
+    };
+  }, [playheadX, editorState.ultraStickyMode]);
+
+  // Regular sticky playhead (fallback when ultra-sticky is disabled)
+  useEffect(() => {
+    if (editorState.ultraStickyMode || !editorState.stickyPlayhead || !scrollContainerRef.current || playheadX === null) return;
+
+    const scrollContainer = scrollContainerRef.current;
+    const containerWidth = scrollContainer.clientWidth;
+    const scrollLeft = scrollContainer.scrollLeft;
+
+    // Calculate if playhead is outside the visible area
+    const playheadPosition = playheadX;
+    const leftEdge = scrollLeft;
+    const rightEdge = scrollLeft + containerWidth;
+
+    // Keep playhead in the center third of the visible area
+    const centerThird = containerWidth / 3;
+    const targetScrollLeft = playheadPosition - centerThird;
+
+    // Only scroll if playhead is getting close to edges or outside visible area
+    if (playheadPosition < leftEdge + centerThird || playheadPosition > rightEdge - centerThird) {
+      scrollContainer.scrollTo({
+        left: Math.max(0, targetScrollLeft),
+        behavior: 'smooth'
+      });
+    }
+  }, [playheadX, editorState.stickyPlayhead, editorState.ultraStickyMode]);
+
   // Debug playhead
   useEffect(() => {
     console.log('🎯 Playhead:', { currentPlayheadTime, referenceStartTime: editorState.referenceStartTime, playheadX });
